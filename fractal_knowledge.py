#!/usr/bin/env python3
"""
Fractal Audio Knowledge Base
Comprehensive documentation and technical knowledge for Fractal Audio products
"""

import re
from typing import Dict, List, Optional, Set, Tuple
from dataclasses import dataclass


@dataclass
class ProductInfo:
    """Information about a Fractal Audio product"""
    name: str
    category: str
    description: str
    key_features: List[str]
    amp_blocks: int
    effects_blocks: int


@dataclass
class TechnicalTopic:
    """Technical topic with detailed information"""
    title: str
    category: str
    description: str
    keywords: List[str]
    content: str
    related_products: List[str]


class FractalKnowledgeBase:
    """Comprehensive Fractal Audio knowledge base"""
    
    def __init__(self):
        self.products = self._initialize_products()
        self.technical_topics = self._initialize_technical_topics()
        self.troubleshooting = self._initialize_troubleshooting()
        self.amp_models = self._initialize_amp_models()
        self.effects_info = self._initialize_effects_info()
        self.keywords_map = self._build_keywords_map()
    
    def _initialize_products(self) -> Dict[str, ProductInfo]:
        """Initialize product information"""
        return {
            "axe_fx_iii": ProductInfo(
                name="Axe-Fx III",
                category="flagship",
                description="Flagship guitar processor with dual amp blocks and comprehensive effects",
                key_features=[
                    "2 Amp blocks with 4 channels each",
                    "Cygnus X-3 amp modeling",
                    "DynaCabs technology",
                    "1GHz dedicated DSP for amp modeling",
                    "USB audio interface",
                    "MIDI control",
                    "Comprehensive effects library"
                ],
                amp_blocks=2,
                effects_blocks=32
            ),
            "fm9": ProductInfo(
                name="FM9",
                category="floor_unit",
                description="Floor-based processor with same amp modeling as Axe-Fx III",
                key_features=[
                    "2 Amp blocks with 4 channels each",
                    "Same amp modeling quality as Axe-Fx III",
                    "Built-in expression pedals",
                    "Footswitches for live performance",
                    "Scene switching",
                    "USB audio interface"
                ],
                amp_blocks=2,
                effects_blocks=24
            ),
            "fm3": ProductInfo(
                name="FM3",
                category="compact",
                description="Compact processor with single high-quality amp block",
                key_features=[
                    "1 Amp block with 4 channels",
                    "Same amp modeling algorithms as Axe-Fx III",
                    "Compact form factor",
                    "Built-in expression pedal",
                    "Scene switching",
                    "USB audio interface"
                ],
                amp_blocks=1,
                effects_blocks=16
            ),
            "vp4": ProductInfo(
                name="VP4",
                category="effects_only",
                description="Dedicated effects processor without amp modeling",
                key_features=[
                    "No amp blocks - effects only",
                    "High-quality reverbs and modulation",
                    "Compact 4-button format",
                    "MIDI control",
                    "Professional studio effects"
                ],
                amp_blocks=0,
                effects_blocks=16
            )
        }
    
    def _initialize_technical_topics(self) -> List[TechnicalTopic]:
        """Initialize technical topics from Fractal Wiki"""
        return [
            TechnicalTopic(
                title="Amp Block Fundamentals",
                category="amp_modeling",
                description="Core concepts of Fractal's amp modeling technology",
                keywords=["amp block", "modeling", "preamp", "power amp", "tubes"],
                content="""
                Fractal Audio's amp modeling uses white-box modeling approach, where every analog 
                component is meticulously measured and digitally simulated. This allows authentic 
                control behavior matching the original amplifiers.
                
                Key concepts:
                - Preamp section: Responsible for tone and gain
                - Power amp section: Provides amplification and character
                - Dynamic frequency response: Constantly varying based on input
                - Bias excursion modeling: Recreates tube amp "breathing"
                - Authentic component tolerances: Models real-world variations
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            ),
            TechnicalTopic(
                title="FRFR vs Traditional Guitar Speakers",
                category="amplification",
                description="Understanding different amplification approaches",
                keywords=["frfr", "full range", "guitar speaker", "cabinet", "amplification"],
                content="""
                Two main approaches for amplification:
                
                FRFR (Full Range Flat Response):
                - Requires cabinet modeling enabled
                - Provides close-miked speaker sound
                - More highs and lows than traditional guitar speakers
                - Best for recording and versatile tones
                - Examples: Studio monitors, FRFR speakers, headphones
                
                Traditional Guitar Speaker:
                - Disable cabinet modeling in processor
                - Familiar "amp in the room" tone
                - Limited to single speaker coloration
                - Requires power amp (neutral recommended)
                - Keep Power Amp Modeling enabled for neutral power amps
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            ),
            TechnicalTopic(
                title="Input and Output Clipping",
                category="signal_flow",
                description="Understanding and preventing digital clipping",
                keywords=["clipping", "input", "output", "levels", "digital"],
                content="""
                Digital clipping sounds harsh and should be avoided:

                Input Clipping:
                - Incoming guitar signal too hot for processor
                - Fix: Reduce Input Sensitivity or enable Input Pad
                - Watch for input clipping LED warnings

                Output Clipping:
                - Signal level too hot for DAC converter
                - Input Level adjustment won't fix this
                - Fix: Reduce Level in Amp block or Output block
                - Use Preset Leveling Tool in editor

                Prevention is key - monitor levels throughout signal chain.
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            ),
            TechnicalTopic(
                title="Scenes and Channels",
                category="preset_management",
                description="Understanding Fractal's preset organization system",
                keywords=["scenes", "channels", "preset", "switching", "performance"],
                content="""
                Fractal devices use a two-tier system for live performance:

                Channels (X/Y/Z/W):
                - Each block can have up to 4 different settings
                - Switch between completely different amp models or effects
                - Independent parameter sets for each channel
                - Great for different gain stages or effect types

                Scenes (8 per preset):
                - Snapshots of all block states and levels
                - Can change multiple parameters simultaneously
                - Perfect for song sections (verse, chorus, solo)
                - Smooth transitions between different sounds

                Best Practice: Use channels for different amp models, scenes for song parts.
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            ),
            TechnicalTopic(
                title="Bias Excursion and Tube Modeling",
                category="amp_modeling",
                description="Advanced tube amp behavior modeling",
                keywords=["bias", "excursion", "tubes", "dynamics", "sag", "compression", "fizz", "noise", "high gain", "definition"],
                content="""
                Bias excursion is key to authentic tube amp feel:

                What it models:
                - How tube bias shifts under different signal levels
                - Creates the "breathing" quality of tube amps
                - Adds harmonic complexity and compression
                - Affects sustain and note decay characteristics

                Parameters to adjust:
                - Master Bias Excursion: Overall amount of bias shifting
                - Power Tube Bias Excursion: Specific to power tubes
                - Preamp Bias Excursion: Affects preamp tube behavior

                Too much bias excursion can cause:
                - Excessive fizz or sizzle
                - Loss of string definition
                - Unwanted intermodulation distortion

                Reduce for cleaner, more defined tones.
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            ),
            TechnicalTopic(
                title="Cabinet Modeling and DynaCabs",
                category="cabinet_modeling",
                description="Understanding cabinet simulation technology",
                keywords=["cabinet", "dynacab", "impulse", "response", "speaker", "microphone"],
                content="""
                Cabinet modeling is crucial for FRFR systems:

                Traditional IR (Impulse Response):
                - Static capture of speaker + microphone combination
                - Fixed microphone position and type
                - Good quality but limited flexibility

                DynaCabs (Dynamic Cabinets):
                - Multiple microphone positions and types
                - Adjustable microphone distance and angle
                - Real-time blending of different mics
                - More realistic speaker behavior modeling

                Key settings:
                - Mic Distance: Closer = more direct, further = more room
                - Mic Angle: On-axis = brighter, off-axis = warmer
                - Room Level: Adds ambient reflections
                - Speaker Drive: Models speaker compression effects

                Disable cabinet modeling when using traditional guitar speakers.
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            ),
            TechnicalTopic(
                title="Volume Knob Cleanup and Dynamics",
                category="amp_modeling",
                description="Making guitar volume knob clean up amp distortion naturally",
                keywords=["volume", "knob", "cleanup", "clean", "dynamics", "feedback", "compression", "gain enhancer"],
                content="""
                Getting natural volume knob cleanup like real tube amps:

                Output Compression with Feedback Type:
                - Set Compressor Type to 'Feedback' in Amp block
                - Add 3-6 dB of Output Compression
                - Creates more distortion when playing hard
                - Cleans up when rolling back guitar volume

                Gain Enhancer Mode:
                - Simulates acoustic reinforcement of loud amp
                - Enhances gain in midrange frequencies
                - More dynamic response to picking attack
                - Great for home/studio playing at lower volumes

                Input Dynamics (Axe-Fx III/FM9):
                - Processes leading edge transients
                - Enhances pick attack sensitivity
                - Works with guitar volume changes

                Setup Tips:
                - Reduce input gain, use picking dynamics instead
                - Set amp gain lower, use compression for drive
                - Combine with Gain Enhancer for best results
                - Modern amps (5153, etc.) respond better than vintage
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            ),
            TechnicalTopic(
                title="Amp Model Selection Guide",
                category="amp_modeling",
                description="Choosing the right amp model for your musical style",
                keywords=["amp", "model", "selection", "metal", "rock", "clean", "blues", "jazz", "style"],
                content="""
                Amp model recommendations by musical style:

                Clean Tones:
                - Band-Commander: Reference clean tone
                - Deluxe Reverb: Classic Fender clean, great pedal platform
                - Twin Reverb: Bright, powerful clean tone
                - AC-30 Normal: Vox chime, edge-of-breakup character

                Blues/Classic Rock:
                - Plexi 50W/100W: Marshall vintage rock, responds to guitar volume
                - JCM800: British rock standard, may need Character adjustment
                - Bassman: Fender bass amp, great for blues
                - AC-30 Top Boost: Vox with more gain and presence

                Modern High-Gain:
                - 5153: Excellent string definition, tight bass response
                - Friedman BE: Boutique high-gain, smooth and singing
                - SLO 100: Soldano lead tone, great for 80s rock/shred
                - Recto: Mesa Boogie modern aggression

                Metal/Extreme:
                - 5153: Best string definition for complex riffs
                - Recto Modern: Tight, aggressive rhythm tone
                - Diezel VH4: German precision high-gain
                - FAS Modern: Idealized modern high-gain design
                """,
                related_products=["axe_fx_iii", "fm9", "fm3"]
            )
        ]
    
    def _initialize_troubleshooting(self) -> Dict[str, Dict[str, str]]:
        """Initialize troubleshooting guides"""
        return {
            "cutting_through_mix": {
                "problem": "Difficulty cutting through band mix with FRFR",
                "causes": "Close-miked sound characteristics, Fletcher-Munson curve effects",
                "solutions": """
                1. Adjust EQ for live context - boost midrange presence
                2. Use Output Compression with Feedback type for dynamics
                3. Consider Gain Enhancer for acoustic reinforcement simulation
                4. Reduce Master Bias Excursion for cleaner sound
                5. Use more modern amp models (5153, etc.) for better definition
                """
            },
            "fizz_and_noise": {
                "problem": "Unwanted fizz, crackle, or noise in high-gain tones",
                "causes": "Bias excursion, intermodulation distortion, cathode follower artifacts",
                "solutions": """
                1. Reduce Master Bias Excursion parameter
                2. Adjust Preamp Bias and Cathode Follower parameters
                3. Use Input Dynamics processing
                4. Consider modern amp models with less bias excursion
                5. Check for input clipping issues
                """
            },
            "string_definition": {
                "problem": "Lack of string definition in high-gain tones",
                "causes": "Vintage amp designs with considerable bias excursion",
                "solutions": """
                1. Turn down Master Bias Excursion
                2. Use modern amp models (5153, etc.)
                3. Adjust Cathode Follower parameters
                4. Use tighter amp models or FAS Modern designs
                5. Consider Output Compression for control
                """
            },
            "volume_knob_cleanup": {
                "problem": "Guitar volume knob doesn't clean up the amp tone",
                "causes": "Need to enhance volume knob sensitivity and dynamics",
                "solutions": """
                1. Set Output Compression Type to 'Feedback'
                2. Add 3-6 dB of Output Compression
                3. Use Gain Enhancer mode for dynamic response
                4. Reduce input gain and use picking dynamics
                5. Adjust Input Dynamics processing if available
                """
            },
            "too_bright_harsh": {
                "problem": "Tone is too bright or harsh, especially at high gain",
                "causes": "High frequency emphasis, lack of speaker compression",
                "solutions": """
                1. Adjust Presence and High Cut controls
                2. Use Speaker Drive or Motor Drive for compression
                3. Try different cabinet models (British vs American)
                4. Adjust microphone positioning in DynaCabs
                5. Use Character parameter (negative values) to tame highs
                """
            },
            "muddy_bass": {
                "problem": "Bass response is muddy or undefined",
                "causes": "Excessive low-end, power supply sag, speaker resonance",
                "solutions": """
                1. Adjust Bass and Depth controls
                2. Reduce Supply Sag parameter
                3. Use High-pass filtering in Input EQ
                4. Adjust Speaker Impedance Curve
                5. Try different cabinet models with tighter bass
                """
            },
            "preset_too_quiet": {
                "problem": "Preset volume is too low compared to others",
                "causes": "Output levels not properly balanced",
                "solutions": """
                1. Use Preset Leveling Tool in editor
                2. Adjust Level in Amp block (not Master Volume)
                3. Check Output block levels
                4. Avoid using Master Volume for level matching
                5. Use Output Compression for consistent levels
                """
            }
        }
    
    def _initialize_amp_models(self) -> Dict[str, Dict[str, str]]:
        """Initialize amp model information"""
        return {
            "reference_tones": {
                "clean": "Band-Commander at default settings with Legacy 103 cab",
                "dirty": "Friedman BE at default settings with Legacy 103 cab",
                "description": "These provide great baseline reference tones for comparison"
            },
            "popular_models": {
                "5153": "Modern high-gain with excellent string definition and tight bass",
                "friedman_be": "Boutique high-gain with smooth, singing character",
                "jcm800": "Classic British rock tone, may need Character adjustment for high gain",
                "slo_100": "Soldano lead tone, great for 80s rock and shred",
                "recto": "Mesa Boogie modern high-gain with aggressive character",
                "ac30": "Vox clean to edge-of-breakup, chimey British tone",
                "deluxe_reverb": "Fender clean platform, great for pedals",
                "plexi": "Marshall vintage rock, responds well to guitar volume",
                "mark_iic": "Mesa Boogie lead tone, complex EQ interactions"
            },
            "amp_categories": {
                "clean_amps": ["Band-Commander", "Deluxe Reverb", "AC-30 Normal", "Twin Reverb"],
                "edge_of_breakup": ["Plexi 50W", "AC-30 Top Boost", "Super Reverb", "Bassman"],
                "classic_rock": ["JCM800", "Plexi 100W", "Super Lead", "JTM45"],
                "modern_high_gain": ["5153", "Recto", "SLO 100", "Friedman BE"],
                "boutique": ["Friedman BE", "Carol-Ann", "Bogner", "Diezel"],
                "vintage_american": ["Deluxe Reverb", "Twin Reverb", "Bassman", "Super Reverb"],
                "british_vintage": ["Plexi", "JTM45", "AC-30", "Vox AC-15"]
            },
            "amp_characteristics": {
                "tight_bass": ["5153", "Recto Modern", "Friedman BE", "Diezel VH4"],
                "loose_vintage": ["Plexi", "JTM45", "Super Lead", "Bassman"],
                "smooth_leads": ["Friedman BE", "SLO 100", "Mark Lead", "Carol-Ann"],
                "aggressive_rhythm": ["Recto", "5150", "JCM800", "Diezel"],
                "pedal_platform": ["Deluxe Reverb", "Twin Reverb", "AC-30 Normal", "Bassman"]
            }
        }
    
    def _initialize_effects_info(self) -> Dict[str, str]:
        """Initialize effects information"""
        return {
            "reverb": "High-quality algorithms including halls, plates, springs, and rooms",
            "delay": "Multiple delay types including analog, digital, tape, and multi-tap",
            "modulation": "Chorus, flanger, phaser, tremolo with authentic vintage modeling",
            "drive": "Overdrive and distortion pedals modeled from classic units",
            "filter": "Wah, auto-wah, and various filter types",
            "pitch": "Pitch shifting, harmonizer, and octave effects",
            "dynamics": "Compressor, gate, limiter for signal control"
        }
    
    def _build_keywords_map(self) -> Dict[str, List[str]]:
        """Build keyword mapping for content retrieval"""
        keywords_map = {}
        
        # Add product keywords
        for product_id, product in self.products.items():
            for feature in product.key_features:
                words = re.findall(r'\b\w+\b', feature.lower())
                for word in words:
                    if word not in keywords_map:
                        keywords_map[word] = []
                    keywords_map[word].append(f"product_{product_id}")
        
        # Add technical topic keywords
        for topic in self.technical_topics:
            for keyword in topic.keywords:
                words = re.findall(r'\b\w+\b', keyword.lower())
                for word in words:
                    if word not in keywords_map:
                        keywords_map[word] = []
                    keywords_map[word].append(f"topic_{topic.title}")
        
        return keywords_map
    
    def find_relevant_content(self, text: str, max_topics: int = 3) -> List[str]:
        """Find relevant content based on input text"""
        text_lower = text.lower()
        relevance_scores = {}
        
        # Score technical topics
        for topic in self.technical_topics:
            score = 0
            for keyword in topic.keywords:
                if keyword.lower() in text_lower:
                    score += 2
            
            # Check for partial matches
            words = re.findall(r'\b\w+\b', text_lower)
            for word in words:
                if word in [k.lower() for k in topic.keywords]:
                    score += 1
            
            if score > 0:
                relevance_scores[topic.title] = score
        
        # Get top scoring topics
        sorted_topics = sorted(relevance_scores.items(), key=lambda x: x[1], reverse=True)
        relevant_content = []
        
        for topic_title, score in sorted_topics[:max_topics]:
            topic = next(t for t in self.technical_topics if t.title == topic_title)
            relevant_content.append(f"**{topic.title}**\n{topic.content}")
        
        return relevant_content
    
    def get_product_info(self, product_name: str) -> Optional[ProductInfo]:
        """Get information about a specific product"""
        product_name_lower = product_name.lower().replace("-", "_").replace(" ", "_")
        
        for product_id, product in self.products.items():
            if (product_id == product_name_lower or 
                product.name.lower().replace("-", "_").replace(" ", "_") == product_name_lower):
                return product
        
        return None
    
    def get_troubleshooting_help(self, issue: str) -> Optional[str]:
        """Get troubleshooting help for specific issues"""
        issue_lower = issue.lower()
        
        for issue_key, info in self.troubleshooting.items():
            if any(keyword in issue_lower for keyword in issue_key.split("_")):
                return f"**Problem:** {info['problem']}\n**Causes:** {info['causes']}\n**Solutions:** {info['solutions']}"
        
        return None
    
    def get_amp_recommendations(self, style: str) -> List[str]:
        """Get amp model recommendations based on musical style"""
        style_lower = style.lower()
        recommendations = []

        style_mappings = {
            "clean": self.amp_models["amp_categories"]["clean_amps"],
            "jazz": ["Twin Reverb", "Deluxe Reverb", "AC-30 Normal"],
            "blues": ["Deluxe Reverb", "Bassman", "Plexi 50W", "AC-30 Top Boost"],
            "rock": ["JCM800", "Plexi 100W", "Super Lead"],
            "classic rock": self.amp_models["amp_categories"]["classic_rock"],
            "metal": ["5153", "Recto", "5150", "Diezel VH4"],
            "modern": self.amp_models["amp_categories"]["modern_high_gain"],
            "vintage": ["Plexi", "JTM45", "Bassman", "AC-30"],
            "country": ["Deluxe Reverb", "Twin Reverb", "Bassman"],
            "ambient": ["AC-30", "Twin Reverb", "Deluxe Reverb"],
            "shred": ["Friedman BE", "SLO 100", "Mark Lead"],
            "djent": ["5153", "Recto Modern", "Friedman BE"]
        }

        for style_key, amps in style_mappings.items():
            if style_key in style_lower:
                recommendations.extend(amps)

        return list(set(recommendations))  # Remove duplicates

    def get_system_instruction_enhancement(self) -> str:
        """Get enhanced system instruction with Fractal knowledge"""
        return """
        You are an expert Fractal Audio assistant with comprehensive knowledge of all Fractal Audio products and technologies.

        FRACTAL AUDIO PRODUCTS:
        - Axe-Fx III: Flagship processor with 2 amp blocks, Cygnus X-3 modeling
        - FM9: Floor unit with same amp modeling as Axe-Fx III, 2 amp blocks
        - FM3: Compact unit with 1 high-quality amp block
        - VP4: Effects-only processor without amp modeling

        CORE TECHNICAL KNOWLEDGE:
        - White-box modeling: Every component measured and simulated
        - Cygnus amp modeling: Latest generation with bias excursion accuracy
        - FRFR vs Traditional speakers: Different approaches require different settings
        - Cabinet modeling: Required for FRFR, disable for traditional guitar speakers
        - Dynamic frequency response: Constantly changing like real tube amps
        - Bias excursion: Key to tube amp feel, adjust for different characteristics
        - Scenes vs Channels: Scenes for song parts, channels for different amp models

        COMMON SOLUTIONS:
        - Cutting through mix: Adjust EQ, use Output Compression, consider Gain Enhancer
        - Fizz/noise: Reduce Master Bias Excursion, adjust Cathode Follower parameters
        - String definition: Use modern amp models, reduce bias excursion
        - Volume knob cleanup: Use Feedback compression, Gain Enhancer mode
        - Reference tones: Band-Commander (clean), Friedman BE (dirty) with Legacy 103 cab

        AMP MODEL CATEGORIES:
        - Clean: Band-Commander, Deluxe Reverb, AC-30 Normal, Twin Reverb
        - Classic Rock: JCM800, Plexi, Super Lead, JTM45
        - Modern High-Gain: 5153, Recto, SLO 100, Friedman BE
        - Boutique: Friedman BE, Carol-Ann, Bogner, Diezel

        Always provide technically accurate, helpful responses based on official Fractal Audio documentation and best practices.
        """
