#!/usr/bin/env python3
"""
Fractal Audio Wiki Content Fetcher
Real-time fetching and parsing of content from wiki.fractalaudio.com
"""

import requests
import re
import time
from typing import Dict, List, Optional, Tuple
from bs4 import BeautifulSoup
from urllib.parse import urljoin, quote
from dataclasses import dataclass
import json
import os
from datetime import datetime, timedelta


@dataclass
class WikiArticle:
    """Represents a Wiki article with metadata"""
    title: str
    url: str
    content: str
    summary: str
    categories: List[str]
    last_updated: datetime
    keywords: List[str]


class FractalWikiFetcher:
    """Fetches and caches content from Fractal Audio Wiki"""
    
    def __init__(self, cache_duration_hours: int = 24):
        self.base_url = "https://wiki.fractalaudio.com"
        self.cache_duration = timedelta(hours=cache_duration_hours)
        self.cache_file = "wiki_cache.json"
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'FractalForumResponseGenerator/1.0 (Educational Use)'
        })
        self._cache = self._load_cache()
        
        # Priority articles to fetch
        self.priority_articles = [
            "Amp_block",
            "Beginners", 
            "FRFR",
            "Cab_block",
            "Scenes",
            "Channels",
            "Input_and_output_clipping",
            "Bias_excursion",
            "Cabinet_modeling",
            "DynaCabs",
            "Effects_list",
            "Amplifier_models_list",
            "Troubleshooting"
        ]
    
    def _load_cache(self) -> Dict:
        """Load cached Wiki content"""
        if os.path.exists(self.cache_file):
            try:
                with open(self.cache_file, 'r', encoding='utf-8') as f:
                    cache_data = json.load(f)
                    # Convert datetime strings back to datetime objects
                    for article_id, article_data in cache_data.items():
                        if 'last_updated' in article_data:
                            article_data['last_updated'] = datetime.fromisoformat(article_data['last_updated'])
                    return cache_data
            except (json.JSONDecodeError, ValueError):
                return {}
        return {}
    
    def _save_cache(self):
        """Save cache to file"""
        try:
            # Convert datetime objects to strings for JSON serialization
            cache_data = {}
            for article_id, article_data in self._cache.items():
                cache_data[article_id] = article_data.copy()
                if 'last_updated' in cache_data[article_id]:
                    cache_data[article_id]['last_updated'] = cache_data[article_id]['last_updated'].isoformat()
            
            with open(self.cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"Warning: Could not save cache: {e}")
    
    def _is_cache_valid(self, article_id: str) -> bool:
        """Check if cached article is still valid"""
        if article_id not in self._cache:
            return False
        
        last_updated = self._cache[article_id].get('last_updated')
        if not last_updated:
            return False
        
        return datetime.now() - last_updated < self.cache_duration
    
    def fetch_article(self, article_title: str, force_refresh: bool = False) -> Optional[WikiArticle]:
        """Fetch a specific Wiki article"""
        article_id = article_title.replace(" ", "_")
        
        # Check cache first
        if not force_refresh and self._is_cache_valid(article_id):
            cached_data = self._cache[article_id]
            return WikiArticle(
                title=cached_data['title'],
                url=cached_data['url'],
                content=cached_data['content'],
                summary=cached_data['summary'],
                categories=cached_data['categories'],
                last_updated=cached_data['last_updated'],
                keywords=cached_data['keywords']
            )
        
        try:
            # Construct Wiki URL
            wiki_url = f"{self.base_url}/wiki/index.php?title={quote(article_id)}"
            
            response = self.session.get(wiki_url, timeout=10)
            response.raise_for_status()
            
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Extract article content
            content_div = soup.find('div', {'id': 'mw-content-text'})
            if not content_div:
                return None
            
            # Extract title
            title_elem = soup.find('h1', {'id': 'firstHeading'})
            title = title_elem.text.strip() if title_elem else article_title
            
            # Extract main content paragraphs
            paragraphs = content_div.find_all(['p', 'h2', 'h3', 'ul', 'ol'])
            content_parts = []
            
            for elem in paragraphs:
                if elem.name in ['h2', 'h3']:
                    content_parts.append(f"\n## {elem.text.strip()}\n")
                elif elem.name == 'p':
                    text = elem.get_text().strip()
                    if text and len(text) > 20:  # Filter out very short paragraphs
                        content_parts.append(text)
                elif elem.name in ['ul', 'ol']:
                    items = elem.find_all('li')
                    for item in items:
                        item_text = item.get_text().strip()
                        if item_text:
                            content_parts.append(f"• {item_text}")
            
            content = '\n\n'.join(content_parts)
            
            # Create summary (first 300 characters)
            summary = content[:300] + "..." if len(content) > 300 else content
            
            # Extract categories
            categories = []
            category_links = soup.find_all('a', href=re.compile(r'/wiki/index\.php\?title=Category:'))
            for link in category_links:
                category = link.text.strip()
                if category and category not in categories:
                    categories.append(category)
            
            # Generate keywords from title and content
            keywords = self._extract_keywords(title, content)
            
            # Create article object
            article = WikiArticle(
                title=title,
                url=wiki_url,
                content=content,
                summary=summary,
                categories=categories,
                last_updated=datetime.now(),
                keywords=keywords
            )
            
            # Cache the article
            self._cache[article_id] = {
                'title': article.title,
                'url': article.url,
                'content': article.content,
                'summary': article.summary,
                'categories': article.categories,
                'last_updated': article.last_updated,
                'keywords': article.keywords
            }
            
            self._save_cache()
            return article
            
        except requests.RequestException as e:
            print(f"Error fetching Wiki article '{article_title}': {e}")
            return None
        except Exception as e:
            print(f"Error parsing Wiki article '{article_title}': {e}")
            return None
    
    def _extract_keywords(self, title: str, content: str) -> List[str]:
        """Extract keywords from title and content"""
        keywords = []
        
        # Add title words
        title_words = re.findall(r'\b\w+\b', title.lower())
        keywords.extend([word for word in title_words if len(word) > 3])
        
        # Extract technical terms from content
        technical_patterns = [
            r'\b(?:amp|amplifier|modeling|modelling)\b',
            r'\b(?:bias|excursion|tube|valve)\b',
            r'\b(?:cabinet|cab|speaker|dynacab)\b',
            r'\b(?:frfr|full.range)\b',
            r'\b(?:scene|channel|preset)\b',
            r'\b(?:reverb|delay|chorus|flanger)\b',
            r'\b(?:eq|equalizer|filter)\b',
            r'\b(?:compression|compressor|gate)\b',
            r'\b(?:input|output|level|gain)\b'
        ]
        
        for pattern in technical_patterns:
            matches = re.findall(pattern, content.lower())
            keywords.extend(matches)
        
        # Remove duplicates and return
        return list(set(keywords))
    
    def search_articles(self, query: str, max_results: int = 5) -> List[WikiArticle]:
        """Search for articles matching the query"""
        query_lower = query.lower()
        scored_articles = []
        
        for article_id, article_data in self._cache.items():
            score = 0
            
            # Score based on title match
            if query_lower in article_data['title'].lower():
                score += 10
            
            # Score based on keyword match
            for keyword in article_data.get('keywords', []):
                if query_lower in keyword.lower():
                    score += 5
            
            # Score based on content match
            if query_lower in article_data['content'].lower():
                score += 1
            
            if score > 0:
                article = WikiArticle(
                    title=article_data['title'],
                    url=article_data['url'],
                    content=article_data['content'],
                    summary=article_data['summary'],
                    categories=article_data['categories'],
                    last_updated=article_data['last_updated'],
                    keywords=article_data['keywords']
                )
                scored_articles.append((score, article))
        
        # Sort by score and return top results
        scored_articles.sort(key=lambda x: x[0], reverse=True)
        return [article for score, article in scored_articles[:max_results]]
    
    def fetch_priority_articles(self) -> Dict[str, WikiArticle]:
        """Fetch all priority articles for the knowledge base"""
        articles = {}
        
        for article_title in self.priority_articles:
            print(f"Fetching Wiki article: {article_title}")
            article = self.fetch_article(article_title)
            if article:
                articles[article_title] = article
            
            # Be respectful to the server
            time.sleep(1)
        
        return articles
    
    def get_cached_articles(self) -> List[str]:
        """Get list of cached article titles"""
        return [data['title'] for data in self._cache.values()]
    
    def clear_cache(self):
        """Clear the article cache"""
        self._cache = {}
        if os.path.exists(self.cache_file):
            os.remove(self.cache_file)
