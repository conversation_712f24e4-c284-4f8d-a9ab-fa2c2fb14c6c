#!/usr/bin/env python3
"""
Fractal Forum Response Generator
Aplikace pro generování odpovědí na diskuze z Fractal Forum pomocí Google Gemini API
"""

import sys
import os
import json
import time
import threading
from datetime import datetime
from typing import Dict, List, Optional, Tuple
import webbrowser

# GUI imports
from PyQt6.QtWidgets import *
from PyQt6.QtCore import *
from PyQt6.QtGui import *

# Web scraping imports
import requests
from bs4 import BeautifulSoup
from urllib.parse import urljoin, urlparse

# Gemini API
import google.generativeai as genai

# Retry logic
from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# Fractal Audio Knowledge Base
from fractal_knowledge import FractalKnowledgeBase


class ForumScraper(QObject):
    """Třída pro získávání dat z Fractal Forum"""
    
    data_loaded = pyqtSignal(dict)
    error_occurred = pyqtSignal(str)
    progress_update = pyqtSignal(str)
    
    def __init__(self):
        super().__init__()
        self.session = requests.Session()
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        self.base_url = "https://forum.fractalaudio.com/"
        self._is_running = True
    
    def stop(self):
        """Zastavit scraper"""
        self._is_running = False
        self.session.close()
        
    def login(self, username: str, password: str) -> bool:
        """Přihlášení do fóra (pokud je vyžadováno)"""
        try:
            if not self._is_running:
                return False
                
            # Získat login stránku
            login_url = urljoin(self.base_url, "login/")
            response = self.session.get(login_url, timeout=30)
            soup = BeautifulSoup(response.content, 'html.parser')
            
            # Najít CSRF token
            csrf_token = soup.find('input', {'name': '_xfToken'})
            if csrf_token:
                csrf_token = csrf_token.get('value', '')
            else:
                self.error_occurred.emit("Nepodařilo se najít CSRF token")
                return False
            
            if not self._is_running:
                return False
            
            # Přihlásit se
            login_data = {
                'login': username,
                'password': password,
                '_xfToken': csrf_token,
                'remember': '1'
            }
            
            response = self.session.post(login_url, data=login_data, timeout=30)
            return response.status_code == 200
            
        except requests.Timeout:
            self.error_occurred.emit("Časový limit při přihlašování vypršel")
            return False
        except Exception as e:
            self.error_occurred.emit(f"Chyba při přihlašování: {str(e)}")
            return False
    
    def fetch_thread(self, thread_url: str, max_pages: Optional[int] = None) -> Optional[Dict]:
        """Načíst konkrétní vlákno z fóra včetně všech nebo omezených stránek"""
        try:
            if not self._is_running:
                return None
            
            all_posts = []
            page_num = 1
            base_url = thread_url.split('?')[0].rstrip('/')
            
            while True:
                if not self._is_running:
                    return None
                
                # Zkontrolovat limit stránek
                if max_pages and page_num > max_pages:
                    self.progress_update.emit(f"Dosažen limit {max_pages} stránek")
                    break
                
                # Sestavit URL pro konkrétní stránku
                if page_num == 1:
                    current_url = thread_url
                else:
                    current_url = f"{base_url}/page-{page_num}"
                
                self.progress_update.emit(f"Načítám stránku {page_num}...")
                
                response = self.session.get(current_url, timeout=30)
                
                if response.status_code != 200:
                    if page_num == 1:
                        self.error_occurred.emit(f"Chyba při načítání: HTTP {response.status_code}")
                        return None
                    else:
                        # Už nejsou další stránky
                        break
                
                soup = BeautifulSoup(response.content, 'html.parser')
                
                # První stránka - získat název vlákna
                if page_num == 1:
                    title_elem = soup.find('h1', class_='p-title-value')
                    title = title_elem.text.strip() if title_elem else "Neznámé vlákno"
                
                # Extrahovat příspěvky z aktuální stránky
                post_elements = soup.find_all('article', class_='message')
                
                if not post_elements:
                    break
                
                for post in post_elements:
                    if not self._is_running:
                        return None
                    
                    # Autor
                    author_elem = post.find('a', class_='username')
                    if not author_elem:
                        author_elem = post.find('span', class_='username')
                    author = author_elem.text.strip() if author_elem else "Anonym"
                    
                    # Čas
                    time_elem = post.find('time')
                    timestamp = time_elem.get('datetime', '') if time_elem else ""
                    
                    # Obsah - vylepšená extrakce
                    content_elem = post.find('div', class_='message-body')
                    if content_elem:
                        # Odstranit citace pro lepší čitelnost
                        for quote in content_elem.find_all('blockquote'):
                            quote.decompose()
                        content = content_elem.text.strip()
                    else:
                        content = ""
                    
                    # ID příspěvku
                    post_id = post.get('id', '')
                    
                    all_posts.append({
                        'id': post_id,
                        'author': author,
                        'timestamp': timestamp,
                        'content': content,
                        'page': page_num
                    })
                
                # Zkontrolovat, zda existuje další stránka
                pagination = soup.find('div', class_='pageNav')
                if pagination:
                    # Hledat odkaz na další stránku
                    next_link = pagination.find('a', class_='pageNav-jump--next')
                    if not next_link:
                        # Zkusit najít čísla stránek
                        page_links = pagination.find_all('a', class_='pageNav-page')
                        current_page_elem = pagination.find('a', class_='pageNav-page--current')
                        
                        if current_page_elem:
                            try:
                                current = int(current_page_elem.text.strip())
                                # Zkontrolovat, zda existuje vyšší stránka
                                has_next = False
                                for link in page_links:
                                    try:
                                        page_no = int(link.text.strip())
                                        if page_no > current:
                                            has_next = True
                                            break
                                    except:
                                        continue
                                
                                if not has_next:
                                    break
                            except:
                                break
                else:
                    # Žádná navigace = jen jedna stránka
                    break
                
                page_num += 1
                
                # Ochrana proti nekonečné smyčce
                if page_num > 100:
                    self.progress_update.emit("Dosažen limit 100 stránek")
                    break
                
                # Malá pauza mezi požadavky
                time.sleep(0.5)
            
            if not all_posts:
                self.error_occurred.emit("Nebyly nalezeny žádné příspěvky")
                return None
            
            thread_data = {
                'url': thread_url,
                'title': title,
                'posts': all_posts,
                'post_count': len(all_posts),
                'page_count': page_num - 1 if page_num > 1 else 1,
                'pages_loaded': min(page_num - 1, max_pages) if max_pages else page_num - 1
            }
            
            pages_info = f"z {page_num - 1}" if page_num > 1 else "z 1"
            if max_pages and page_num - 1 > max_pages:
                pages_info = f"z {max_pages} (limit)"
            
            self.progress_update.emit(f"Načteno {len(all_posts)} příspěvků {pages_info} stránek")
            self.data_loaded.emit(thread_data)
            return thread_data
            
        except requests.Timeout:
            self.error_occurred.emit("Časový limit vypršel - zkuste to znovu")
            return None
        except Exception as e:
            self.error_occurred.emit(f"Chyba při načítání vlákna: {str(e)}")
            return None
    
    def fetch_forum_section(self, section_url: str) -> Optional[List[Dict]]:
        """Načíst seznam vláken ze sekce fóra"""
        try:
            if not self._is_running:
                return None
                
            self.progress_update.emit("Načítám sekci fóra...")
            response = self.session.get(section_url, timeout=30)
            
            if response.status_code != 200:
                return None
            
            if not self._is_running:
                return None
            
            soup = BeautifulSoup(response.content, 'html.parser')
            threads = []
            
            # Najít všechna vlákna
            thread_elements = soup.find_all('div', class_='structItem-title')
            
            for thread in thread_elements:
                if not self._is_running:
                    return None
                    
                link = thread.find('a')
                if link:
                    threads.append({
                        'title': link.text.strip(),
                        'url': urljoin(self.base_url, link.get('href', ''))
                    })
            
            return threads
            
        except requests.Timeout:
            self.error_occurred.emit("Časový limit vypršel")
            return None
        except Exception as e:
            self.error_occurred.emit(f"Chyba při načítání sekce: {str(e)}")
            return None


class GeminiClient(QObject):
    """Klient pro komunikaci s Google Gemini API"""

    response_ready = pyqtSignal(str)
    error_occurred = pyqtSignal(str)
    progress_update = pyqtSignal(str)

    def __init__(self, api_key: str):
        super().__init__()
        self.api_key = api_key
        self.model = None
        self._is_running = True
        self.knowledge_base = FractalKnowledgeBase()
        self.initialize_model()

    def stop(self):
        """Zastavit klienta"""
        self._is_running = False
    
    def initialize_model(self):
        """Inicializovat Gemini model"""
        try:
            genai.configure(api_key=self.api_key)

            # Get enhanced system instruction with Fractal knowledge - in English
            base_instruction = """
            You are an expert Fractal Audio assistant specializing in Fractal Audio products.
            Your task is to generate relevant, friendly, and technically accurate responses
            for Fractal Audio forum discussions.

            IMPORTANT: Always respond in English, regardless of the input language.

            Rules:
            1. Always be helpful and friendly
            2. Provide technically accurate information
            3. Respect the discussion context
            4. Always respond in English
            5. Maintain a professional tone typical for music communities
            """

            # Add comprehensive Fractal knowledge
            enhanced_instruction = base_instruction + "\n" + self.knowledge_base.get_system_instruction_enhancement()

            self.model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",
                generation_config={
                    "temperature": 0.8,
                    "top_p": 0.95,
                    "max_output_tokens": 2000,
                },
                system_instruction=enhanced_instruction
            )
            
        except Exception as e:
            self.error_occurred.emit(f"Chyba při inicializaci Gemini: {str(e)}")
    
    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=2, max=10),
        retry=retry_if_exception_type(Exception)
    )
    def generate_response(self, context: str, query: str, tone: str = "friendly") -> str:
        """Generovat odpověď pomocí Gemini API"""
        try:
            if not self._is_running:
                return ""
                
            self.progress_update.emit("Generuji odpověď...")
            
            # Připravit prompt
            prompt = self._prepare_prompt(context, query, tone)
            
            if not self._is_running:
                return ""
            
            # Generovat odpověď
            response = self.model.generate_content(prompt)
            
            if response.text and self._is_running:
                self.response_ready.emit(response.text)
                return response.text
            else:
                if self._is_running:
                    self.error_occurred.emit("Gemini nevrátilo žádnou odpověď")
                return ""
                
        except Exception as e:
            if self._is_running:
                self.error_occurred.emit(f"Chyba Gemini API: {str(e)}")
            raise
    
    def _prepare_prompt(self, context: str, query: str, tone: str) -> str:
        """Prepare prompt for Gemini with relevant documentation"""
        tone_instructions = {
            "friendly": "Be friendly and welcoming",
            "technical": "Focus on technical details and be precise",
            "helpful": "Be maximally helpful and provide specific advice",
            "brief": "Respond concisely and clearly"
        }

        # Find relevant documentation based on context and query (enhanced with Wiki and parameters)
        combined_text = f"{context} {query}"
        relevant_docs = self.knowledge_base.find_relevant_content(
            combined_text,
            max_topics=2,
            include_wiki=True,
            include_parameters=True
        )

        # Check for troubleshooting scenarios
        troubleshooting_help = self.knowledge_base.get_troubleshooting_help(combined_text)

        # Get amp recommendations if musical style is mentioned
        amp_recommendations = self.knowledge_base.get_amp_recommendations(combined_text)

        # Find specific parameters mentioned in the discussion
        mentioned_parameters = self.knowledge_base.find_parameters_in_text(combined_text)

        # Build enhanced prompt
        prompt = f"""
DISCUSSION CONTEXT:
{context}

QUESTION/TOPIC TO RESPOND TO:
{query}"""

        # Add relevant documentation if found
        if relevant_docs:
            prompt += f"""

RELEVANT TECHNICAL DOCUMENTATION:
{chr(10).join(relevant_docs)}"""

        # Add troubleshooting help if applicable
        if troubleshooting_help:
            prompt += f"""

TROUBLESHOOTING GUIDE:
{troubleshooting_help}"""

        # Add amp recommendations if relevant
        if amp_recommendations:
            prompt += f"""

RECOMMENDED AMP MODELS:
{', '.join(amp_recommendations[:5])}"""

        # Add specific parameter explanations if parameters were mentioned
        if mentioned_parameters:
            prompt += f"""

PARAMETER EXPLANATIONS:
"""
            for param in mentioned_parameters[:2]:  # Limit to 2 parameters to avoid prompt bloat
                param_explanation = self.knowledge_base.parameter_db.format_parameter_explanation(param)
                prompt += f"{param_explanation}\n"

        prompt += f"""

TONE INSTRUCTIONS:
{tone_instructions.get(tone, tone_instructions['friendly'])}

Generate a relevant response that:
1. Directly responds to the discussion and question
2. Is technically accurate for Fractal Audio products
3. Uses the provided documentation for precise information
4. Fits the forum context
5. Is written in natural English language

RESPONSE:
"""
        return prompt


class ForumWorkerThread(QThread):
    """Worker thread pro asynchronní operace"""
    
    finished_signal = pyqtSignal()
    error_signal = pyqtSignal(str)
    
    def __init__(self, target_func, *args, **kwargs):
        super().__init__()
        self.target_func = target_func
        self.args = args
        self.kwargs = kwargs
        self._is_running = True
    
    def run(self):
        """Spustit funkci ve vlákně"""
        try:
            if self._is_running:
                self.target_func(*self.args, **self.kwargs)
        except Exception as e:
            self.error_signal.emit(f"Chyba ve worker threadu: {e}")
        finally:
            self.finished_signal.emit()
    
    def stop(self):
        """Zastavit thread"""
        self._is_running = False


class MainWindow(QMainWindow):
    """Hlavní okno aplikace"""
    
    def __init__(self):
        super().__init__()
        self.forum_scraper = ForumScraper()
        self.gemini_client = None
        self.current_thread_data = None
        self.settings = QSettings('FractalForumApp', 'Settings')
        self.active_threads = []  # Seznam aktivních threadů
        
        self.init_ui()
        self.load_settings()
        self.setup_connections()
    
    def closeEvent(self, event):
        """Správně ukončit aplikaci"""
        # Zastavit forum scraper
        self.forum_scraper.stop()
        
        # Zastavit Gemini klienta
        if self.gemini_client:
            self.gemini_client.stop()
        
        # Zastavit všechny běžící thready
        for thread in self.active_threads:
            if thread.isRunning():
                thread.stop()
                thread.quit()
                thread.wait(1000)  # Počkat max 1 sekundu
        
        # Uložit nastavení
        self.save_settings()
        event.accept()
    
    def init_ui(self):
        """Inicializovat uživatelské rozhraní"""
        self.setWindowTitle("Fractal Forum Response Generator")
        self.setGeometry(100, 100, 1400, 900)
        
        # Nastavit ikonu
        self.setWindowIcon(QIcon())
        
        # Hlavní widget
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # Hlavní layout
        main_layout = QVBoxLayout(central_widget)
        
        # Toolbar
        self.create_toolbar()
        
        # Splitter pro hlavní obsah
        splitter = QSplitter(Qt.Orientation.Horizontal)
        
        # Levý panel - Seznam vláken a nastavení
        left_panel = self.create_left_panel()
        
        # Střední panel - Obsah vlákna
        middle_panel = self.create_middle_panel()
        
        # Pravý panel - Gemini odpovědi
        right_panel = self.create_right_panel()
        
        # Přidat panely do splitteru
        splitter.addWidget(left_panel)
        splitter.addWidget(middle_panel)
        splitter.addWidget(right_panel)
        
        # Nastavit poměry
        splitter.setSizes([300, 600, 500])
        
        main_layout.addWidget(splitter)
        
        # Status bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")
    
    def create_toolbar(self):
        """Vytvořit toolbar"""
        toolbar = QToolBar()
        self.addToolBar(toolbar)
        
        # Actions
        load_action = QAction("📂 Load Thread", self)
        load_action.triggered.connect(self.load_thread_dialog)
        toolbar.addAction(load_action)

        refresh_action = QAction("🔄 Refresh", self)
        refresh_action.triggered.connect(self.refresh_current_thread)
        toolbar.addAction(refresh_action)

        toolbar.addSeparator()

        settings_action = QAction("⚙️ Settings", self)
        settings_action.triggered.connect(self.show_settings_dialog)
        toolbar.addAction(settings_action)

        toolbar.addSeparator()

        help_action = QAction("❓ Help", self)
        help_action.triggered.connect(self.show_help)
        toolbar.addAction(help_action)

        toolbar.addSeparator()

        docs_action = QAction("📚 Documentation", self)
        docs_action.triggered.connect(self.show_documentation_browser)
        toolbar.addAction(docs_action)

        param_lookup_action = QAction("🔍 Parameters", self)
        param_lookup_action.triggered.connect(self.show_parameter_lookup)
        toolbar.addAction(param_lookup_action)
    
    def create_left_panel(self) -> QWidget:
        """Vytvořit levý panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Title
        title = QLabel("Settings and History")
        title.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        layout.addWidget(title)

        # URL input
        url_group = QGroupBox("Thread URL")
        url_layout = QVBoxLayout()

        self.url_input = QLineEdit()
        self.url_input.setPlaceholderText("https://forum.fractalaudio.com/threads/...")
        self.url_input.returnPressed.connect(self.load_thread)
        url_layout.addWidget(self.url_input)

        # Load settings
        load_settings_layout = QHBoxLayout()

        self.load_all_pages_check = QCheckBox("Load all pages")
        self.load_all_pages_check.setChecked(True)
        self.load_all_pages_check.toggled.connect(self.on_load_pages_toggled)
        load_settings_layout.addWidget(self.load_all_pages_check)

        self.max_pages_spin = QSpinBox()
        self.max_pages_spin.setMinimum(1)
        self.max_pages_spin.setMaximum(50)
        self.max_pages_spin.setValue(10)
        self.max_pages_spin.setPrefix("Max: ")
        self.max_pages_spin.setSuffix(" pages")
        self.max_pages_spin.setEnabled(False)
        load_settings_layout.addWidget(self.max_pages_spin)

        url_layout.addLayout(load_settings_layout)

        load_button = QPushButton("Load Thread")
        load_button.clicked.connect(self.load_thread)
        url_layout.addWidget(load_button)
        
        url_group.setLayout(url_layout)
        layout.addWidget(url_group)
        
        # Response tone
        tone_group = QGroupBox("Response Tone")
        tone_layout = QVBoxLayout()

        self.tone_combo = QComboBox()
        self.tone_combo.addItems([
            "Friendly",
            "Technical",
            "Helpful",
            "Brief"
        ])
        tone_layout.addWidget(self.tone_combo)

        tone_group.setLayout(tone_layout)
        layout.addWidget(tone_group)

        # History
        history_group = QGroupBox("Thread History")
        history_layout = QVBoxLayout()

        self.history_list = QListWidget()
        self.history_list.itemDoubleClicked.connect(self.load_from_history)
        history_layout.addWidget(self.history_list)

        clear_history_btn = QPushButton("Clear History")
        clear_history_btn.clicked.connect(self.clear_history)
        history_layout.addWidget(clear_history_btn)
        
        history_group.setLayout(history_layout)
        layout.addWidget(history_group)
        
        layout.addStretch()
        
        return panel
    
    def on_load_pages_toggled(self, checked):
        """Přepnout nastavení načítání stránek"""
        self.max_pages_spin.setEnabled(not checked)
    
    def create_middle_panel(self) -> QWidget:
        """Vytvořit střední panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Title
        self.thread_title = QLabel("No thread loaded")
        self.thread_title.setStyleSheet("font-weight: bold; font-size: 16px; padding: 5px;")
        self.thread_title.setWordWrap(True)
        layout.addWidget(self.thread_title)

        # Thread content
        self.thread_content = QTextEdit()
        self.thread_content.setReadOnly(True)
        layout.addWidget(self.thread_content)

        # Context question
        context_group = QGroupBox("Context for Response")
        context_layout = QVBoxLayout()

        self.context_input = QTextEdit()
        self.context_input.setPlaceholderText(
            "Enter a specific question or context for generating a response...\n"
            "For example: 'How to set up delay for ambient sound?' or "
            "'Help with metal tone settings'"
        )
        self.context_input.setMaximumHeight(100)
        context_layout.addWidget(self.context_input)

        generate_btn = QPushButton("🤖 Generate Response")
        generate_btn.clicked.connect(self.generate_response)
        generate_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                font-weight: bold;
                padding: 8px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        context_layout.addWidget(generate_btn)
        
        context_group.setLayout(context_layout)
        layout.addWidget(context_group)
        
        return panel
    
    def create_right_panel(self) -> QWidget:
        """Vytvořit pravý panel"""
        panel = QWidget()
        layout = QVBoxLayout(panel)
        
        # Title
        title = QLabel("Generated Response")
        title.setStyleSheet("font-weight: bold; font-size: 14px; padding: 5px;")
        layout.addWidget(title)

        # Response
        self.response_text = QTextEdit()
        self.response_text.setPlaceholderText(
            "Generated response from Gemini API will appear here..."
        )
        layout.addWidget(self.response_text)

        # Response action buttons
        button_layout = QHBoxLayout()

        copy_btn = QPushButton("📋 Copy")
        copy_btn.clicked.connect(self.copy_response)
        button_layout.addWidget(copy_btn)

        save_btn = QPushButton("💾 Save")
        save_btn.clicked.connect(self.save_response)
        button_layout.addWidget(save_btn)

        regenerate_btn = QPushButton("🔄 Regenerate")
        regenerate_btn.clicked.connect(self.regenerate_response)
        button_layout.addWidget(regenerate_btn)
        
        layout.addLayout(button_layout)
        
        return panel
    
    def setup_connections(self):
        """Nastavit signály a sloty"""
        # Forum scraper
        self.forum_scraper.data_loaded.connect(self.on_thread_loaded)
        self.forum_scraper.error_occurred.connect(self.on_error)
        self.forum_scraper.progress_update.connect(self.update_status)
        
    def load_settings(self):
        """Načíst uložená nastavení"""
        # API klíč
        api_key = self.settings.value('gemini_api_key', '')
        if api_key:
            self.gemini_client = GeminiClient(api_key)
            self.gemini_client.response_ready.connect(self.on_response_ready)
            self.gemini_client.error_occurred.connect(self.on_error)
            self.gemini_client.progress_update.connect(self.update_status)
        
        # Nastavení načítání stránek
        load_all = self.settings.value('load_all_pages', True, type=bool)
        self.load_all_pages_check.setChecked(load_all)
        
        max_pages = self.settings.value('max_pages', 10, type=int)
        self.max_pages_spin.setValue(max_pages)
        
        # Historie - nový formát s URL
        history_data = self.settings.value('thread_history_data', {})
        if isinstance(history_data, dict):
            for url, text in history_data.items():
                item = QListWidgetItem(text)
                item.setData(Qt.ItemDataRole.UserRole, url)
                self.history_list.addItem(item)
        else:
            # Zpětná kompatibilita se starým formátem
            history = self.settings.value('thread_history', [])
            if isinstance(history, list):
                for item_text in history[:20]:
                    if isinstance(item_text, str):
                        item = QListWidgetItem(item_text)
                        self.history_list.addItem(item)
    
    def save_settings(self):
        """Uložit nastavení"""
        # Nastavení načítání stránek
        self.settings.setValue('load_all_pages', self.load_all_pages_check.isChecked())
        self.settings.setValue('max_pages', self.max_pages_spin.value())
        
        # Historie - uložit s URL
        history_data = {}
        for i in range(min(self.history_list.count(), 20)):  # Omezit na 20 položek
            item = self.history_list.item(i)
            if item:
                url = item.data(Qt.ItemDataRole.UserRole)
                if url:
                    history_data[url] = item.text()
        
        self.settings.setValue('thread_history_data', history_data)
    
    def load_thread_dialog(self):
        """Dialog pro načtení vlákna"""
        url, ok = QInputDialog.getText(
            self,
            "Načíst vlákno",
            "Zadejte URL vlákna z Fractal Forum:",
            text=self.url_input.text()
        )
        
        if ok and url:
            self.url_input.setText(url)
            self.load_thread()
    
    def load_thread(self):
        """Načíst vlákno z URL"""
        url = self.url_input.text().strip()
        
        if not url:
            QMessageBox.warning(self, "Chyba", "Zadejte prosím URL vlákna")
            return
        
        # Zkontrolovat, zda je URL z Fractal Forum
        if "forum.fractalaudio.com" not in url:
            QMessageBox.warning(
                self,
                "Upozornění",
                "URL musí být z forum.fractalaudio.com"
            )
            return
        
        # Získat nastavení načítání
        max_pages = None
        if not self.load_all_pages_check.isChecked():
            max_pages = self.max_pages_spin.value()
        
        # Spustit načítání ve vlákně
        self.update_status("Načítám vlákno...")
        
        # Vytvořit a spustit worker thread
        thread = ForumWorkerThread(
            self.forum_scraper.fetch_thread,
            url,
            max_pages
        )
        
        # Připojit signály
        thread.finished_signal.connect(lambda: self.on_thread_finished(thread))
        thread.error_signal.connect(self.on_error)
        
        # Přidat do seznamu aktivních threadů
        self.active_threads.append(thread)
        
        # Spustit thread
        thread.start()
    
    def on_thread_finished(self, thread):
        """Odstranit dokončený thread ze seznamu"""
        if thread in self.active_threads:
            self.active_threads.remove(thread)
            thread.deleteLater()
    
    def on_thread_loaded(self, thread_data: dict):
        """Zpracovat načtené vlákno"""
        self.current_thread_data = thread_data
        
        # Aktualizovat UI s informací o stránkách
        pages_info = ""
        if thread_data.get('pages_loaded', 1) > 1:
            pages_info = f" ({thread_data.get('pages_loaded')} stránek)"
        elif thread_data.get('page_count', 1) > 1:
            pages_info = f" ({thread_data.get('page_count')} stránek)"
        
        self.thread_title.setText(f"{thread_data['title']}{pages_info}")
        
        # Zobrazit příspěvky
        content_html = """
        <style>
            body { font-family: Arial, sans-serif; }
            .post { margin-bottom: 20px; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
            .post-header { font-weight: bold; color: #333; }
            .post-content { margin-top: 10px; color: #555; }
            .page-marker { background-color: #f0f0f0; padding: 5px; margin: 10px 0; text-align: center; font-weight: bold; }
        </style>
        """
        
        current_page = 0
        for post in thread_data['posts']:
            # Vložit značku nové stránky
            post_page = post.get('page', 1)
            if post_page > current_page:
                current_page = post_page
                content_html += f'<div class="page-marker">--- Stránka {current_page} ---</div>'
            
            # Formátovat čas
            timestamp = post['timestamp']
            if timestamp:
                try:
                    dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
                    formatted_time = dt.strftime('%d.%m.%Y %H:%M')
                except:
                    formatted_time = timestamp
            else:
                formatted_time = "Neznámý čas"
            
            content_html += f"""
            <div class="post">
                <div class="post-header">
                    {post['author']} - {formatted_time}
                </div>
                <div class="post-content">
                    {post['content']}
                </div>
            </div>
            """
        
        self.thread_content.setHtml(content_html)
        
        # Přidat do historie
        pages_loaded = thread_data.get('pages_loaded', thread_data.get('page_count', 1))
        pages_text = f", {pages_loaded} str." if pages_loaded > 1 else ""
        history_item = f"{thread_data['title'][:50]}... ({thread_data['post_count']} příspěvků{pages_text})"
        
        # Zkontrolovat, zda už není v historii
        exists = False
        for i in range(self.history_list.count()):
            item = self.history_list.item(i)
            if item and item.data(Qt.ItemDataRole.UserRole) == thread_data['url']:
                exists = True
                break
        
        if not exists:
            item = QListWidgetItem(history_item)
            item.setData(Qt.ItemDataRole.UserRole, thread_data['url'])
            self.history_list.insertItem(0, item)
            
            # Omezit historii na 20 položek
            while self.history_list.count() > 20:
                self.history_list.takeItem(20)
        
        # Uložit nastavení
        self.save_settings()
        
        # Zobrazit finální status
        status_msg = f"Načteno {thread_data['post_count']} příspěvků"
        if pages_loaded > 1:
            status_msg += f" ze {pages_loaded} stránek"
        self.update_status(status_msg)
    
    def generate_response(self):
        """Generovat odpověď pomocí Gemini"""
        if not self.gemini_client:
            QMessageBox.warning(
                self,
                "Chyba",
                "Nejdříve nastavte Gemini API klíč v nastavení"
            )
            self.show_settings_dialog()
            return
        
        if not self.current_thread_data:
            QMessageBox.warning(
                self,
                "Chyba",
                "Nejdříve načtěte vlákno z fóra"
            )
            return
        
        # Získat kontext
        query = self.context_input.toPlainText().strip()
        if not query:
            query = "Napiš relevantní odpověď do této diskuze"
        
        # Připravit kontext z vlákna
        context = f"Název vlákna: {self.current_thread_data['title']}\n"
        context += f"Celkový počet příspěvků: {self.current_thread_data['post_count']}\n"
        
        pages_loaded = self.current_thread_data.get('pages_loaded', self.current_thread_data.get('page_count', 1))
        if pages_loaded > 1:
            context += f"Načteno stránek: {pages_loaded}\n"
            # Informovat, pokud nebyly načteny všechny stránky
            if 'page_count' in self.current_thread_data and self.current_thread_data['page_count'] > pages_loaded:
                context += f"(Poznámka: Vlákno má celkem {self.current_thread_data['page_count']} stránek, ale byl nastaven limit)\n"
        
        context += "\n"
        
        # Vzít více příspěvků z posledních stránek (až 15)
        recent_posts = self.current_thread_data['posts'][-15:]
        
        # Přidat informaci o prvních příspěvcích pro kontext
        if len(self.current_thread_data['posts']) > 15:
            context += "ZAČÁTEK DISKUZE:\n"
            for post in self.current_thread_data['posts'][:3]:
                context += f"{post['author']}: {post['content'][:200]}...\n\n"
            context += "...\n\n"
        
        context += "NEDÁVNÉ PŘÍSPĚVKY:\n"
        for post in recent_posts:
            # Omezit délku jednotlivých příspěvků
            content = post['content'][:500]
            if len(post['content']) > 500:
                content += "..."
            
            page_info = f" (str. {post.get('page', 1)})" if pages_loaded > 1 else ""
            context += f"{post['author']}{page_info}: {content}\n\n"
        
        # Získat tón
        tone_map = {
            "Přátelský": "friendly",
            "Technický": "technical",
            "Nápomocný": "helpful",
            "Stručný": "brief"
        }
        tone = tone_map.get(self.tone_combo.currentText(), "friendly")
        
        # Spustit generování ve vlákně
        self.response_text.clear()
        self.update_status("Generuji odpověď...")
        
        # Vytvořit a spustit worker thread
        thread = ForumWorkerThread(
            self.gemini_client.generate_response,
            context,
            query,
            tone
        )
        
        # Připojit signály
        thread.finished_signal.connect(lambda: self.on_thread_finished(thread))
        thread.error_signal.connect(self.on_error)
        
        # Přidat do seznamu aktivních threadů
        self.active_threads.append(thread)
        
        # Spustit thread
        thread.start()
    
    def on_response_ready(self, response: str):
        """Zpracovat vygenerovanou odpověď"""
        self.response_text.setPlainText(response)
        self.update_status("Odpověď vygenerována")
    
    def on_error(self, error_msg: str):
        """Zpracovat chybu"""
        QMessageBox.critical(self, "Chyba", error_msg)
        self.update_status(f"Chyba: {error_msg}")
    
    def update_status(self, message: str):
        """Aktualizovat status bar"""
        self.status_bar.showMessage(message)
    
    def copy_response(self):
        """Kopírovat odpověď do schránky"""
        response = self.response_text.toPlainText()
        if response:
            QApplication.clipboard().setText(response)
            self.update_status("Odpověď zkopírována do schránky")
    
    def save_response(self):
        """Uložit odpověď do souboru"""
        response = self.response_text.toPlainText()
        if not response:
            QMessageBox.warning(self, "Chyba", "Není co uložit")
            return
        
        filename, _ = QFileDialog.getSaveFileName(
            self,
            "Uložit odpověď",
            f"odpoved_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt",
            "Text Files (*.txt)"
        )
        
        if filename:
            try:
                with open(filename, 'w', encoding='utf-8') as f:
                    f.write(f"Vlákno: {self.current_thread_data['title']}\n")
                    f.write(f"URL: {self.current_thread_data['url']}\n")
                    f.write(f"Vygenerováno: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                    f.write("-" * 50 + "\n\n")
                    f.write(response)
                
                self.update_status(f"Odpověď uložena do {filename}")
            except Exception as e:
                QMessageBox.critical(self, "Chyba", f"Nepodařilo se uložit soubor: {str(e)}")
    
    def regenerate_response(self):
        """Regenerovat odpověď"""
        # Zkontrolovat, zda neběží jiný thread
        for thread in self.active_threads:
            if thread.isRunning():
                QMessageBox.information(
                    self,
                    "Upozornění", 
                    "Počkejte prosím, až se dokončí aktuální generování"
                )
                return
        
        self.generate_response()
    
    def refresh_current_thread(self):
        """Obnovit aktuální vlákno"""
        if self.current_thread_data:
            # Zkontrolovat, zda už neběží jiný thread
            for thread in self.active_threads:
                if thread.isRunning():
                    QMessageBox.information(
                        self,
                        "Upozornění",
                        "Počkejte prosím, až se dokončí aktuální operace"
                    )
                    return
            
            self.url_input.setText(self.current_thread_data['url'])
            self.load_thread()
    
    def load_from_history(self, item):
        """Načíst vlákno z historie"""
        if not item:
            return
            
        url = item.data(Qt.ItemDataRole.UserRole)
        if url:
            # Zkontrolovat, zda už neběží jiný thread
            for thread in self.active_threads:
                if thread.isRunning():
                    QMessageBox.information(
                        self,
                        "Upozornění",
                        "Počkejte prosím, až se dokončí aktuální operace"
                    )
                    return
            
            self.url_input.setText(url)
            self.load_thread()
    
    def clear_history(self):
        """Vymazat historii"""
        reply = QMessageBox.question(
            self,
            "Potvrzení",
            "Opravdu chcete vymazat celou historii?",
            QMessageBox.StandardButton.Yes | QMessageBox.StandardButton.No
        )
        
        if reply == QMessageBox.StandardButton.Yes:
            self.history_list.clear()
            self.save_settings()
            self.update_status("Historie vymazána")
    
    def show_settings_dialog(self):
        """Zobrazit dialog nastavení"""
        dialog = SettingsDialog(self)
        if dialog.exec() == QDialog.DialogCode.Accepted:
            api_key = dialog.api_key_input.text().strip()
            
            if api_key:
                self.settings.setValue('gemini_api_key', api_key)
                
                # Zastavit starého klienta
                if self.gemini_client:
                    self.gemini_client.stop()
                
                # Reinicializovat Gemini klienta
                self.gemini_client = GeminiClient(api_key)
                self.gemini_client.response_ready.connect(self.on_response_ready)
                self.gemini_client.error_occurred.connect(self.on_error)
                self.gemini_client.progress_update.connect(self.update_status)
                
                self.update_status("Gemini API klíč nastaven")
            
            # Uložit přihlašovací údaje (pokud byly zadány)
            username = dialog.username_input.text().strip()
            password = dialog.password_input.text().strip()
            
            if username and password:
                # Pro bezpečnost: v reálné aplikaci byste měli šifrovat heslo
                self.settings.setValue('forum_username', username)
                # VAROVÁNÍ: Ukládání hesla v plain textu není bezpečné!
                # V produkční aplikaci použijte keyring nebo jiné bezpečné úložiště
    
    def show_help(self):
        """Show help dialog"""
        help_text = """
        <h2>Fractal Forum Response Generator</h2>

        <h3>How to use the application:</h3>
        <ol>
            <li><b>API Key Setup:</b> Click ⚙️ Settings and enter your Google Gemini API key</li>
            <li><b>Load Thread:</b> Paste a thread URL from Fractal Forum and click "Load Thread"</li>
            <li><b>Generate Response:</b> Enter context or question and click "Generate Response"</li>
            <li><b>Work with Response:</b> You can copy, save, or regenerate the response</li>
        </ol>

        <h3>Loading Multi-page Threads:</h3>
        <ul>
            <li>Application automatically loads all thread pages</li>
            <li>For long threads, you can limit the number of loaded pages</li>
            <li>Uncheck "Load all pages" and set a limit</li>
            <li>0.5s pause between pages for ethical scraping</li>
        </ul>

        <h3>Enhanced Features:</h3>
        <ul>
            <li><b>📚 Documentation:</b> Access comprehensive Fractal Audio documentation</li>
            <li><b>🔍 Parameters:</b> Quick lookup for specific Fractal parameters</li>
            <li><b>Wiki Integration:</b> Real-time access to Fractal Audio Wiki content</li>
            <li><b>Smart Responses:</b> AI automatically includes relevant technical information</li>
        </ul>

        <h3>Tips:</h3>
        <ul>
            <li>Choose appropriate response tone based on discussion context</li>
            <li>Be specific in context input for better results</li>
            <li>History automatically saves last 20 loaded threads</li>
            <li>For long threads, consider limiting page count</li>
            <li>All responses are generated in English for consistency</li>
        </ul>

        <h3>Important:</h3>
        <p><b>This application requires permission from Fractal Audio Systems for forum access!</b></p>
        <p>Respect forum rules and copyright.</p>
        """

        QMessageBox.information(self, "Help", help_text)

    def show_documentation_browser(self):
        """Zobrazit prohlížeč dokumentace"""
        if not hasattr(self, 'knowledge_base'):
            self.knowledge_base = FractalKnowledgeBase()

        dialog = DocumentationBrowser(self, self.knowledge_base)
        dialog.exec()

    def show_parameter_lookup(self):
        """Zobrazit rychlé vyhledávání parametrů"""
        if not hasattr(self, 'knowledge_base'):
            self.knowledge_base = FractalKnowledgeBase()

        dialog = ParameterLookupDialog(self, self.knowledge_base.parameter_db)
        dialog.exec()


class ParameterLookupDialog(QDialog):
    """Rychlé vyhledávání parametrů"""

    def __init__(self, parent=None, parameter_db=None):
        super().__init__(parent)
        self.parameter_db = parameter_db
        self.init_ui()

    def init_ui(self):
        """Initialize UI"""
        self.setWindowTitle("Quick Parameter Lookup")
        self.setModal(True)
        self.resize(600, 500)

        layout = QVBoxLayout(self)

        # Search field
        search_layout = QHBoxLayout()
        search_label = QLabel("Search parameter:")
        search_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter parameter name or keyword...")
        self.search_input.textChanged.connect(self.search_parameters)
        search_layout.addWidget(self.search_input)

        layout.addLayout(search_layout)

        # Results list
        self.results_list = QListWidget()
        self.results_list.itemClicked.connect(self.show_parameter_details)
        layout.addWidget(self.results_list)

        # Parameter details area
        self.details_text = QTextEdit()
        self.details_text.setReadOnly(True)
        self.details_text.setMaximumHeight(200)
        self.details_text.setPlainText("Select a parameter from the list to view details.")
        layout.addWidget(self.details_text)

        # Buttons
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)

        layout.addLayout(button_layout)

        # Naplnit všechny parametry na začátku
        self.populate_all_parameters()

        # Focus na vyhledávací pole
        self.search_input.setFocus()

    def populate_all_parameters(self):
        """Naplnit seznam všemi parametry"""
        if not self.parameter_db:
            return

        self.results_list.clear()
        for param_id, param_info in self.parameter_db.parameters.items():
            item = QListWidgetItem(f"{param_info.name} - {param_info.category}")
            item.setData(Qt.ItemDataRole.UserRole, param_info)
            self.results_list.addItem(item)

    def search_parameters(self, text):
        """Vyhledat parametry podle textu"""
        if not self.parameter_db:
            return

        self.results_list.clear()

        if not text.strip():
            self.populate_all_parameters()
            return

        # Najít relevantní parametry
        parameters = self.parameter_db.find_parameters(text, max_results=10)

        for param_info in parameters:
            item = QListWidgetItem(f"{param_info.name} - {param_info.category}")
            item.setData(Qt.ItemDataRole.UserRole, param_info)
            self.results_list.addItem(item)

    def show_parameter_details(self, item):
        """Zobrazit detaily vybraného parametru"""
        param_info = item.data(Qt.ItemDataRole.UserRole)
        if param_info:
            details = self.parameter_db.format_parameter_explanation(param_info)
            # Convert markdown-style formatting to HTML
            details_html = details.replace("**", "<b>").replace("**", "</b>")
            details_html = details_html.replace("*", "<i>").replace("*", "</i>")
            details_html = details_html.replace("• ", "• ")
            details_html = details_html.replace("\n", "<br>")

            self.details_text.setHtml(details_html)


class DocumentationBrowser(QDialog):
    """Prohlížeč Fractal Audio dokumentace"""

    def __init__(self, parent=None, knowledge_base=None):
        super().__init__(parent)
        self.knowledge_base = knowledge_base or FractalKnowledgeBase()
        self.init_ui()

    def init_ui(self):
        """Initialize browser UI"""
        self.setWindowTitle("Fractal Audio Documentation")
        self.setModal(False)
        self.resize(1100, 800)

        layout = QHBoxLayout(self)

        # Left panel - categories and topics
        left_panel = QWidget()
        left_layout = QVBoxLayout(left_panel)

        # Search
        search_label = QLabel("Search:")
        left_layout.addWidget(search_label)

        self.search_input = QLineEdit()
        self.search_input.setPlaceholderText("Enter keyword...")
        self.search_input.textChanged.connect(self.filter_topics)
        left_layout.addWidget(self.search_input)

        # Tabs for different content types
        content_tabs = QTabWidget()
        left_layout.addWidget(content_tabs)

        # Tab 1: Technical topics
        topics_tab = QWidget()
        topics_layout = QVBoxLayout(topics_tab)

        self.topics_list = QListWidget()
        self.populate_topics_list()
        self.topics_list.itemClicked.connect(self.show_topic_content)
        topics_layout.addWidget(self.topics_list)
        content_tabs.addTab(topics_tab, "Topics")

        # Tab 2: Products
        products_tab = QWidget()
        products_layout = QVBoxLayout(products_tab)

        self.products_list = QListWidget()
        self.populate_products_list()
        self.products_list.itemClicked.connect(self.show_product_info)
        products_layout.addWidget(self.products_list)
        content_tabs.addTab(products_tab, "Products")

        # Tab 3: Parameters
        parameters_tab = QWidget()
        parameters_layout = QVBoxLayout(parameters_tab)

        self.parameters_list = QListWidget()
        self.populate_parameters_list()
        self.parameters_list.itemClicked.connect(self.show_parameter_info)
        parameters_layout.addWidget(self.parameters_list)
        content_tabs.addTab(parameters_tab, "Parameters")

        # Tab 4: Wiki articles
        wiki_tab = QWidget()
        wiki_layout = QVBoxLayout(wiki_tab)

        wiki_buttons_layout = QHBoxLayout()
        refresh_wiki_btn = QPushButton("🔄 Refresh Cache")
        refresh_wiki_btn.clicked.connect(self.refresh_wiki_cache)
        wiki_buttons_layout.addWidget(refresh_wiki_btn)

        fetch_article_btn = QPushButton("📥 Fetch Article")
        fetch_article_btn.clicked.connect(self.fetch_specific_article)
        wiki_buttons_layout.addWidget(fetch_article_btn)

        wiki_layout.addLayout(wiki_buttons_layout)

        self.wiki_list = QListWidget()
        self.populate_wiki_list()
        self.wiki_list.itemClicked.connect(self.show_wiki_content)
        wiki_layout.addWidget(self.wiki_list)
        content_tabs.addTab(wiki_tab, "Wiki")

        left_panel.setMaximumWidth(300)
        layout.addWidget(left_panel)

        # Right panel - content
        right_panel = QWidget()
        right_layout = QVBoxLayout(right_panel)

        self.content_title = QLabel("Select a topic or product")
        self.content_title.setStyleSheet("font-weight: bold; font-size: 16px; margin-bottom: 10px;")
        right_layout.addWidget(self.content_title)

        self.content_text = QTextEdit()
        self.content_text.setReadOnly(True)
        self.content_text.setPlainText("Select a topic from the list on the left to view detailed information.")
        right_layout.addWidget(self.content_text)

        layout.addWidget(right_panel)

        # Close button
        button_layout = QHBoxLayout()
        button_layout.addStretch()

        close_button = QPushButton("Close")
        close_button.clicked.connect(self.accept)
        button_layout.addWidget(close_button)

        right_layout.addLayout(button_layout)

    def populate_topics_list(self):
        """Naplnit seznam technických témat"""
        for topic in self.knowledge_base.technical_topics:
            item = QListWidgetItem(topic.title)
            item.setData(Qt.ItemDataRole.UserRole, topic)
            self.topics_list.addItem(item)

    def populate_products_list(self):
        """Naplnit seznam produktů"""
        for product_id, product in self.knowledge_base.products.items():
            item = QListWidgetItem(product.name)
            item.setData(Qt.ItemDataRole.UserRole, product)
            self.products_list.addItem(item)

    def populate_parameters_list(self):
        """Naplnit seznam parametrů"""
        for param_id, param_info in self.knowledge_base.parameter_db.parameters.items():
            item = QListWidgetItem(param_info.name)
            item.setData(Qt.ItemDataRole.UserRole, param_info)
            self.parameters_list.addItem(item)

    def populate_wiki_list(self):
        """Naplnit seznam Wiki článků"""
        if self.knowledge_base.wiki_fetcher:
            cached_articles = self.knowledge_base.wiki_fetcher.get_cached_articles()
            for article_title in cached_articles:
                item = QListWidgetItem(article_title)
                item.setData(Qt.ItemDataRole.UserRole, article_title)
                self.wiki_list.addItem(item)

    def filter_topics(self, text):
        """Filtrovat obsah podle vyhledávacího textu"""
        search_text = text.lower()

        # Filter topics
        for i in range(self.topics_list.count()):
            item = self.topics_list.item(i)
            topic = item.data(Qt.ItemDataRole.UserRole)

            visible = (search_text in topic.title.lower() or
                      any(search_text in keyword.lower() for keyword in topic.keywords) or
                      search_text in topic.content.lower())

            item.setHidden(not visible)

        # Filter products
        for i in range(self.products_list.count()):
            item = self.products_list.item(i)
            product = item.data(Qt.ItemDataRole.UserRole)

            visible = (search_text in product.name.lower() or
                      search_text in product.description.lower() or
                      any(search_text in feature.lower() for feature in product.key_features))

            item.setHidden(not visible)

        # Filter parameters
        for i in range(self.parameters_list.count()):
            item = self.parameters_list.item(i)
            param = item.data(Qt.ItemDataRole.UserRole)

            visible = (search_text in param.name.lower() or
                      search_text in param.description.lower() or
                      any(search_text in keyword.lower() for keyword in param.keywords))

            item.setHidden(not visible)

        # Filter Wiki articles
        for i in range(self.wiki_list.count()):
            item = self.wiki_list.item(i)
            article_title = item.data(Qt.ItemDataRole.UserRole)

            visible = search_text in article_title.lower()
            item.setHidden(not visible)

    def show_topic_content(self, item):
        """Zobrazit obsah vybraného tématu"""
        topic = item.data(Qt.ItemDataRole.UserRole)
        if topic:
            self.content_title.setText(topic.title)

            content = f"""
<h2>{topic.title}</h2>
<p><strong>Kategorie:</strong> {topic.category}</p>
<p><strong>Klíčová slova:</strong> {', '.join(topic.keywords)}</p>
<p><strong>Relevantní produkty:</strong> {', '.join(topic.related_products)}</p>

<h3>Popis:</h3>
<p>{topic.description}</p>

<h3>Podrobnosti:</h3>
<div style="white-space: pre-wrap;">{topic.content}</div>
"""
            self.content_text.setHtml(content)

    def show_product_info(self, item):
        """Zobrazit informace o vybraném produktu"""
        product = item.data(Qt.ItemDataRole.UserRole)
        if product:
            self.content_title.setText(product.name)

            content = f"""
<h2>{product.name}</h2>
<p><strong>Kategorie:</strong> {product.category}</p>
<p><strong>Amp bloky:</strong> {product.amp_blocks}</p>
<p><strong>Effects bloky:</strong> {product.effects_blocks}</p>

<h3>Popis:</h3>
<p>{product.description}</p>

<h3>Klíčové vlastnosti:</h3>
<ul>
"""
            for feature in product.key_features:
                content += f"<li>{feature}</li>"

            content += "</ul>"

            self.content_text.setHtml(content)

    def show_parameter_info(self, item):
        """Zobrazit informace o vybraném parametru"""
        param_info = item.data(Qt.ItemDataRole.UserRole)
        if param_info:
            self.content_title.setText(param_info.name)

            content = f"""
<h2>{param_info.name}</h2>
<p><strong>Category:</strong> {param_info.category}</p>
<p><strong>Range:</strong> {param_info.range_info}</p>
<p><strong>Default Value:</strong> {param_info.default_value}</p>
<p><strong>Products:</strong> {', '.join(param_info.products)}</p>

<h3>Description:</h3>
<p>{param_info.description}</p>

<h3>Usage Tips:</h3>
<ul>
"""
            for tip in param_info.usage_tips:
                content += f"<li>{tip}</li>"

            content += "</ul>"

            if param_info.related_parameters:
                content += f"""
<h3>Related Parameters:</h3>
<p>{', '.join(param_info.related_parameters)}</p>
"""

            self.content_text.setHtml(content)

    def show_wiki_content(self, item):
        """Zobrazit obsah Wiki článku"""
        article_title = item.data(Qt.ItemDataRole.UserRole)
        if article_title and self.knowledge_base.wiki_fetcher:
            # Try to get from cache first
            article = self.knowledge_base.get_wiki_article(article_title)
            if article:
                self.content_title.setText(f"Wiki: {article.title}")

                content = f"""
<h2>{article.title}</h2>
<p><strong>URL:</strong> <a href="{article.url}">{article.url}</a></p>
<p><strong>Categories:</strong> {', '.join(article.categories)}</p>
<p><strong>Last Updated:</strong> {article.last_updated.strftime('%m/%d/%Y %H:%M')}</p>

<h3>Content:</h3>
<div style="white-space: pre-wrap;">{article.content}</div>
"""
                self.content_text.setHtml(content)
            else:
                self.content_text.setPlainText("Failed to load Wiki article.")

    def refresh_wiki_cache(self):
        """Obnovit cache Wiki článků"""
        if self.knowledge_base.wiki_fetcher:
            self.content_text.setPlainText("Refreshing Wiki cache... Please wait.")
            QApplication.processEvents()  # Update UI

            try:
                articles = self.knowledge_base.refresh_wiki_cache()
                self.wiki_list.clear()
                self.populate_wiki_list()

                self.content_text.setHtml(f"""
<h2>Wiki Cache Refreshed</h2>
<p>Successfully loaded {len(articles)} articles from Fractal Audio Wiki.</p>
<h3>Loaded Articles:</h3>
<ul>
""")
                content = self.content_text.toHtml()
                for article_name in articles.keys():
                    content += f"<li>{article_name}</li>"
                content += "</ul>"

                self.content_text.setHtml(content)

            except Exception as e:
                self.content_text.setPlainText(f"Error refreshing Wiki cache: {str(e)}")

    def fetch_specific_article(self):
        """Načíst konkrétní Wiki článek"""
        if not self.knowledge_base.wiki_fetcher:
            QMessageBox.warning(self, "Error", "Wiki fetcher is not available.")
            return

        article_name, ok = QInputDialog.getText(
            self,
            "Fetch Wiki Article",
            "Enter article name (e.g., 'Amp_block', 'FRFR', 'Beginners'):"
        )

        if ok and article_name.strip():
            self.content_text.setPlainText(f"Loading article '{article_name}'... Please wait.")
            QApplication.processEvents()

            try:
                article = self.knowledge_base.wiki_fetcher.fetch_article(article_name.strip(), force_refresh=True)
                if article:
                    # Add to Wiki list if not already there
                    existing_items = [self.wiki_list.item(i).data(Qt.ItemDataRole.UserRole)
                                    for i in range(self.wiki_list.count())]
                    if article.title not in existing_items:
                        item = QListWidgetItem(article.title)
                        item.setData(Qt.ItemDataRole.UserRole, article.title)
                        self.wiki_list.addItem(item)

                    # Show the article content
                    self.content_title.setText(f"Wiki: {article.title}")
                    content = f"""
<h2>{article.title}</h2>
<p><strong>URL:</strong> <a href="{article.url}">{article.url}</a></p>
<p><strong>Categories:</strong> {', '.join(article.categories)}</p>
<p><strong>Loaded:</strong> {article.last_updated.strftime('%m/%d/%Y %H:%M')}</p>

<h3>Content:</h3>
<div style="white-space: pre-wrap;">{article.content}</div>
"""
                    self.content_text.setHtml(content)
                else:
                    self.content_text.setPlainText(f"Failed to load article '{article_name}'. Please check the article name.")

            except Exception as e:
                self.content_text.setPlainText(f"Error loading article: {str(e)}")


class SettingsDialog(QDialog):
    """Dialog pro nastavení"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.settings = QSettings('FractalForumApp', 'Settings')
        self.init_ui()
    
    def init_ui(self):
        """Inicializovat UI dialogu"""
        self.setWindowTitle("Nastavení")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # Gemini API
        api_group = QGroupBox("Google Gemini API")
        api_layout = QVBoxLayout()
        
        api_label = QLabel("API klíč:")
        api_layout.addWidget(api_label)
        
        self.api_key_input = QLineEdit()
        self.api_key_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.api_key_input.setPlaceholderText("Zadejte váš Gemini API klíč")
        
        # Načíst existující klíč
        saved_key = self.settings.value('gemini_api_key', '')
        if saved_key:
            self.api_key_input.setText(saved_key)
        
        api_layout.addWidget(self.api_key_input)
        
        api_help = QLabel(
            '<a href="https://makersuite.google.com/app/apikey">Získat API klíč</a>'
        )
        api_help.setOpenExternalLinks(True)
        api_layout.addWidget(api_help)
        
        api_group.setLayout(api_layout)
        layout.addWidget(api_group)
        
        # Forum přihlášení (volitelné)
        forum_group = QGroupBox("Přihlášení do fóra (volitelné)")
        forum_layout = QFormLayout()
        
        self.username_input = QLineEdit()
        self.username_input.setPlaceholderText("Uživatelské jméno")
        forum_layout.addRow("Uživatel:", self.username_input)
        
        self.password_input = QLineEdit()
        self.password_input.setEchoMode(QLineEdit.EchoMode.Password)
        self.password_input.setPlaceholderText("Heslo")
        forum_layout.addRow("Heslo:", self.password_input)
        
        forum_note = QLabel(
            "<small>⚠️ Přihlašovací údaje jsou potřeba pouze pro privátní sekce fóra</small>"
        )
        forum_note.setWordWrap(True)
        forum_layout.addRow(forum_note)
        
        forum_group.setLayout(forum_layout)
        layout.addWidget(forum_group)
        
        # Další nastavení
        other_group = QGroupBox("Další nastavení")
        other_layout = QFormLayout()
        
        self.timeout_spin = QSpinBox()
        self.timeout_spin.setRange(5, 60)
        self.timeout_spin.setValue(30)
        self.timeout_spin.setSuffix(" sekund")
        other_layout.addRow("Timeout požadavků:", self.timeout_spin)
        
        self.cache_check = QCheckBox("Používat cache pro načtená vlákna")
        self.cache_check.setChecked(True)
        other_layout.addRow(self.cache_check)
        
        other_group.setLayout(other_layout)
        layout.addWidget(other_group)
        
        layout.addStretch()
        
        # Tlačítka
        button_box = QDialogButtonBox(
            QDialogButtonBox.StandardButton.Ok | 
            QDialogButtonBox.StandardButton.Cancel
        )
        button_box.accepted.connect(self.accept)
        button_box.rejected.connect(self.reject)
        layout.addWidget(button_box)


def main():
    """Hlavní funkce aplikace"""
    app = QApplication(sys.argv)
    
    # Nastavit styl aplikace
    app.setStyle('Fusion')
    
    # Tmavé téma (volitelné)
    palette = QPalette()
    palette.setColor(QPalette.ColorRole.Window, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.WindowText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Base, QColor(25, 25, 25))
    palette.setColor(QPalette.ColorRole.AlternateBase, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ToolTipBase, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.ToolTipText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Text, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.Button, QColor(53, 53, 53))
    palette.setColor(QPalette.ColorRole.ButtonText, Qt.GlobalColor.white)
    palette.setColor(QPalette.ColorRole.BrightText, Qt.GlobalColor.red)
    palette.setColor(QPalette.ColorRole.Link, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.Highlight, QColor(42, 130, 218))
    palette.setColor(QPalette.ColorRole.HighlightedText, Qt.GlobalColor.black)
    
    app.setPalette(palette)
    
    # Vytvořit a zobrazit hlavní okno
    window = MainWindow()
    window.show()
    
    # Zobrazit uvítací zprávu
    QTimer.singleShot(
        500,
        lambda: window.update_status(
            "Vítejte! Nejdříve nastavte Gemini API klíč v nastavení."
        )
    )
    
    sys.exit(app.exec())


if __name__ == "__main__":
    main()