#!/usr/bin/env python3
"""
Fractal Audio Parameter Database
Comprehensive database of Fractal Audio parameters with detailed explanations
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import re


@dataclass
class ParameterInfo:
    """Information about a specific Fractal Audio parameter"""
    name: str
    category: str
    description: str
    range_info: str
    default_value: str
    usage_tips: List[str]
    related_parameters: List[str]
    products: List[str]
    keywords: List[str]


class FractalParameterDatabase:
    """Database of Fractal Audio parameters with detailed explanations"""
    
    def __init__(self):
        self.parameters = self._initialize_parameters()
        self.parameter_map = self._build_parameter_map()
    
    def _initialize_parameters(self) -> Dict[str, ParameterInfo]:
        """Initialize the parameter database"""
        return {
            "master_bias_excursion": ParameterInfo(
                name="Master Bias Excursion",
                category="Amp Block - Dynamics",
                description="Controls the overall amount of bias shifting in the virtual tubes, affecting the 'breathing' quality and harmonic complexity of the amp",
                range_info="0.0 to 10.0 (default varies by amp model)",
                default_value="Varies by amp model (typically 3.0-7.0)",
                usage_tips=[
                    "Reduce for cleaner, more defined tones",
                    "Increase for more vintage tube amp character",
                    "Lower values help with string definition in high-gain tones",
                    "Higher values add warmth but can cause fizz",
                    "Modern amps typically use lower values than vintage"
                ],
                related_parameters=["Power Tube Bias Excursion", "Preamp Bias Excursion", "Cathode Follower Compression"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["bias", "excursion", "dynamics", "tube", "breathing", "fizz", "definition"]
            ),
            
            "output_compression": ParameterInfo(
                name="Output Compression",
                category="Amp Block - Dynamics",
                description="Controls compression at the output of the amp block, with different types for various behaviors",
                range_info="0.0 to 10.0",
                default_value="0.0",
                usage_tips=[
                    "Use 'Output' type for simple compression after the amp",
                    "Use 'Feedback' type for volume knob cleanup behavior",
                    "Use 'Gain Enhancer' for acoustic reinforcement simulation",
                    "3-6 dB typical for volume knob cleanup",
                    "Adjust threshold for different compression behavior"
                ],
                related_parameters=["Output Comp Type", "Output Comp Threshold", "Output Comp Clarity"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["compression", "output", "feedback", "gain enhancer", "volume", "cleanup", "dynamics"]
            ),
            
            "cabinet_modeling": ParameterInfo(
                name="Cabinet Modeling",
                category="Setup - Global",
                description="Global setting that enables or disables cabinet modeling throughout the processor",
                range_info="On/Off",
                default_value="On",
                usage_tips=[
                    "Enable for FRFR speakers, studio monitors, headphones",
                    "Disable when using traditional guitar speakers",
                    "Must be enabled for DynaCabs to function",
                    "Affects all cabinet blocks in all presets",
                    "Can be overridden per preset if needed"
                ],
                related_parameters=["Power Amp Modeling", "Speaker Drive", "Motor Drive"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["cabinet", "modeling", "frfr", "speaker", "dynacab", "setup", "global"]
            ),
            
            "power_amp_modeling": ParameterInfo(
                name="Power Amp Modeling",
                category="Setup - Global",
                description="Global setting that enables or disables power amp modeling",
                range_info="On/Off", 
                default_value="On",
                usage_tips=[
                    "Enable when using neutral power amps or FRFR",
                    "Disable when using guitar amp heads/combos as power amps",
                    "Keep enabled for most modern setups",
                    "Affects the power amp section of all amp models",
                    "Disable if using tube power amp for coloration"
                ],
                related_parameters=["Cabinet Modeling", "Master Volume", "Presence", "Depth"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["power amp", "modeling", "setup", "global", "neutral", "tube"]
            ),
            
            "input_sensitivity": ParameterInfo(
                name="Input Sensitivity",
                category="Setup - I/O",
                description="Adjusts the input sensitivity to match your guitar's output level",
                range_info="-12 dB to +12 dB",
                default_value="0 dB",
                usage_tips=[
                    "Reduce if you see input clipping warnings",
                    "Adjust so input LED occasionally flickers yellow",
                    "Higher output pickups may need negative values",
                    "Single coils typically need higher sensitivity",
                    "Use Input Pad for very hot signals"
                ],
                related_parameters=["Input Pad", "Input Trim", "Input Level"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["input", "sensitivity", "clipping", "level", "guitar", "pickup"]
            ),
            
            "speaker_drive": ParameterInfo(
                name="Speaker Drive",
                category="Cab Block",
                description="Models the magnetic compression and distortion that occurs in guitar speakers",
                range_info="0.0 to 10.0",
                default_value="0.0",
                usage_tips=[
                    "Adds compression and harmonic distortion like real speakers",
                    "Typical values: 2-6 for vintage character",
                    "Higher values for more speaker breakup",
                    "Combines well with Motor Drive",
                    "Use sparingly - a little goes a long way"
                ],
                related_parameters=["Motor Drive", "Speaker Compression", "Speaker Breakup"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["speaker", "drive", "compression", "distortion", "breakup", "vintage"]
            ),
            
            "dynacab_distance": ParameterInfo(
                name="DynaCab Distance",
                category="Cab Block - DynaCab",
                description="Controls the virtual microphone distance from the speaker",
                range_info="0.0 to 10.0",
                default_value="5.0",
                usage_tips=[
                    "Lower values = closer mic = more direct sound",
                    "Higher values = farther mic = more room ambience",
                    "0-3: Close miking for tight, focused sound",
                    "4-6: Medium distance for balanced tone",
                    "7-10: Far miking for ambient, spacious sound"
                ],
                related_parameters=["DynaCab Angle", "Room Level", "Mic Type"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["dynacab", "distance", "microphone", "close", "far", "room", "ambient"]
            ),
            
            "scene": ParameterInfo(
                name="Scene",
                category="Preset Management",
                description="Snapshots of all block states and parameter values within a preset",
                range_info="1-8 scenes per preset",
                default_value="Scene 1",
                usage_tips=[
                    "Use for different song sections (verse, chorus, solo)",
                    "Can change multiple parameters simultaneously",
                    "Smooth transitions between scenes",
                    "Each scene remembers block bypass states",
                    "Use Scene MIDI block for automatic changes"
                ],
                related_parameters=["Channels", "Scene MIDI", "Controllers"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["scene", "preset", "snapshot", "song", "section", "performance"]
            ),
            
            "channels": ParameterInfo(
                name="Channels (X/Y/Z/W)",
                category="Block Management",
                description="Independent parameter sets within each block, allowing completely different settings",
                range_info="Up to 4 channels per block (X, Y, Z, W)",
                default_value="Channel X",
                usage_tips=[
                    "Use for different amp models in same block",
                    "Great for clean/dirty switching",
                    "Each channel has independent parameters",
                    "Can be controlled via scenes or direct switching",
                    "Z and W channels available on newer firmware"
                ],
                related_parameters=["Scenes", "Controllers", "MIDI"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["channel", "x", "y", "z", "w", "switching", "amp", "model"]
            ),

            "gain": ParameterInfo(
                name="Gain",
                category="Amp Block - Tone",
                description="Controls the amount of preamp gain/distortion in the virtual amplifier",
                range_info="0.0 to 10.0",
                default_value="Varies by amp model",
                usage_tips=[
                    "Start with moderate settings and adjust to taste",
                    "Higher gain = more distortion but less clarity",
                    "Use Input Trim for fine gain adjustments",
                    "Modern amps typically have more gain available",
                    "Combine with Output Compression for controlled dynamics"
                ],
                related_parameters=["Input Trim", "Master Volume", "Output Level"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["gain", "distortion", "preamp", "drive", "overdrive"]
            ),

            "presence": ParameterInfo(
                name="Presence",
                category="Amp Block - Tone",
                description="Controls high-frequency emphasis in the power amp section, affecting brightness and clarity",
                range_info="0.0 to 10.0",
                default_value="Varies by amp model",
                usage_tips=[
                    "Increase for brighter, more cutting tone",
                    "Decrease for warmer, smoother highs",
                    "Interacts with High Cut parameter",
                    "More effective at higher Master Volume settings",
                    "Adjust based on speaker/cabinet choice"
                ],
                related_parameters=["High Cut", "Depth", "Treble"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["presence", "brightness", "highs", "power amp", "clarity"]
            ),

            "master_volume": ParameterInfo(
                name="Master Volume",
                category="Amp Block - Tone",
                description="Controls the virtual power amp drive and overall amp character, not output level",
                range_info="0.0 to 10.0",
                default_value="Varies by amp model",
                usage_tips=[
                    "Use for power amp saturation and compression",
                    "Higher settings add power tube distortion",
                    "Does NOT control output level - use Level parameter",
                    "Affects Presence and Depth control effectiveness",
                    "Sweet spot varies by amp model (typically 3-7)"
                ],
                related_parameters=["Level", "Power Tube Type", "Supply Sag"],
                products=["Axe-Fx III", "FM9", "FM3"],
                keywords=["master volume", "power amp", "saturation", "compression", "level"]
            )
        }
    
    def _build_parameter_map(self) -> Dict[str, List[str]]:
        """Build keyword mapping for parameter lookup"""
        param_map = {}
        
        for param_id, param_info in self.parameters.items():
            # Add parameter name words
            name_words = re.findall(r'\b\w+\b', param_info.name.lower())
            for word in name_words:
                if word not in param_map:
                    param_map[word] = []
                param_map[word].append(param_id)
            
            # Add keywords
            for keyword in param_info.keywords:
                if keyword not in param_map:
                    param_map[keyword] = []
                param_map[keyword].append(param_id)
        
        return param_map
    
    def find_parameters(self, text: str, max_results: int = 3) -> List[ParameterInfo]:
        """Find parameters mentioned in text"""
        text_lower = text.lower()
        parameter_scores = {}
        
        # Score parameters based on keyword matches
        for param_id, param_info in self.parameters.items():
            score = 0
            
            # Check parameter name
            if param_info.name.lower() in text_lower:
                score += 10
            
            # Check keywords
            for keyword in param_info.keywords:
                if keyword in text_lower:
                    score += 2
            
            # Check category
            if param_info.category.lower() in text_lower:
                score += 1
            
            if score > 0:
                parameter_scores[param_id] = score
        
        # Sort by score and return top results
        sorted_params = sorted(parameter_scores.items(), key=lambda x: x[1], reverse=True)
        return [self.parameters[param_id] for param_id, score in sorted_params[:max_results]]
    
    def get_parameter(self, parameter_name: str) -> Optional[ParameterInfo]:
        """Get specific parameter by name"""
        param_name_lower = parameter_name.lower().replace(" ", "_").replace("-", "_")
        
        for param_id, param_info in self.parameters.items():
            if (param_id == param_name_lower or 
                param_info.name.lower().replace(" ", "_").replace("-", "_") == param_name_lower):
                return param_info
        
        return None
    
    def get_parameters_by_category(self, category: str) -> List[ParameterInfo]:
        """Get all parameters in a specific category"""
        return [param for param in self.parameters.values() 
                if category.lower() in param.category.lower()]
    
    def format_parameter_explanation(self, param_info: ParameterInfo) -> str:
        """Format parameter information for display"""
        explanation = f"**{param_info.name}**\n"
        explanation += f"*Category: {param_info.category}*\n\n"
        explanation += f"{param_info.description}\n\n"
        explanation += f"**Range:** {param_info.range_info}\n"
        explanation += f"**Default:** {param_info.default_value}\n\n"
        
        if param_info.usage_tips:
            explanation += "**Usage Tips:**\n"
            for tip in param_info.usage_tips:
                explanation += f"• {tip}\n"
            explanation += "\n"
        
        if param_info.related_parameters:
            explanation += f"**Related Parameters:** {', '.join(param_info.related_parameters)}\n"
        
        return explanation
