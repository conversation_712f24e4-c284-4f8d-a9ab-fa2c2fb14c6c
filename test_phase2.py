#!/usr/bin/env python3
"""
Test script for Phase 2 enhancements
"""

from fractal_knowledge import FractalKnowledgeBase
from fractal_wiki_fetcher import FractalWikiFetcher
from fractal_parameters import FractalParameterDatabase

def test_phase2_features():
    """Test Phase 2 enhanced features"""
    print("Testing Phase 2 Fractal Audio Documentation Integration...")
    
    # Test 1: Enhanced Knowledge Base
    print("\n=== ENHANCED KNOWLEDGE BASE ===")
    kb = FractalKnowledgeBase(enable_wiki_fetching=False)  # Disable Wiki for quick test
    
    # Test enhanced content finding
    test_queries = [
        "I have fizz in my high gain tone and need help with bias excursion",
        "How do I set up FRFR speakers with cabinet modeling?",
        "My master volume doesn't seem to affect the output level",
        "What's the difference between scenes and channels?"
    ]
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        relevant_docs = kb.find_relevant_content(
            query, 
            max_topics=2, 
            include_wiki=False,  # Skip Wiki for this test
            include_parameters=True
        )
        
        if relevant_docs:
            for i, doc in enumerate(relevant_docs):
                print(f"Result {i+1}: {doc[:100]}...")
        else:
            print("No relevant content found")
    
    # Test 2: Parameter Database
    print("\n=== PARAMETER DATABASE ===")
    param_db = FractalParameterDatabase()
    
    parameter_queries = [
        "master bias excursion",
        "output compression",
        "gain distortion",
        "presence brightness"
    ]
    
    for query in parameter_queries:
        print(f"\nParameter Query: {query}")
        parameters = param_db.find_parameters(query, max_results=2)
        
        for param in parameters:
            print(f"Found: {param.name} - {param.description[:80]}...")
    
    # Test 3: Parameter Explanation Formatting
    print("\n=== PARAMETER EXPLANATIONS ===")
    master_bias = param_db.get_parameter("Master Bias Excursion")
    if master_bias:
        explanation = param_db.format_parameter_explanation(master_bias)
        print(f"Master Bias Excursion explanation:\n{explanation[:300]}...")
    
    # Test 4: Wiki Fetcher (if enabled)
    print("\n=== WIKI FETCHER TEST ===")
    try:
        wiki_fetcher = FractalWikiFetcher()
        print("Wiki fetcher initialized successfully")
        
        # Test article fetching (just check if it works, don't actually fetch)
        cached_articles = wiki_fetcher.get_cached_articles()
        print(f"Cached articles: {len(cached_articles)}")
        
        if cached_articles:
            print(f"Sample cached articles: {cached_articles[:3]}")
        
    except Exception as e:
        print(f"Wiki fetcher test skipped: {e}")
    
    # Test 5: Enhanced System Instruction
    print("\n=== ENHANCED SYSTEM INSTRUCTION ===")
    enhanced_instruction = kb.get_system_instruction_enhancement()
    print(f"Enhanced instruction length: {len(enhanced_instruction)} characters")
    
    # Check for key enhancements
    enhancements = [
        "real-time Wiki content",
        "parameter explanations", 
        "DynaCabs",
        "Master Bias Excursion",
        "Output Compression"
    ]
    
    for enhancement in enhancements:
        if enhancement in enhanced_instruction:
            print(f"✓ Contains: {enhancement}")
        else:
            print(f"✗ Missing: {enhancement}")
    
    # Test 6: Integration Test
    print("\n=== INTEGRATION TEST ===")
    test_text = "I'm having trouble with fizz in my high gain metal tone using the 5153 amp model. The master bias excursion seems too high."
    
    print(f"Test text: {test_text}")
    
    # Find relevant content
    relevant_content = kb.find_relevant_content(test_text, include_parameters=True)
    print(f"Found {len(relevant_content)} relevant content pieces")
    
    # Find parameters
    parameters = kb.find_parameters_in_text(test_text)
    print(f"Found {len(parameters)} relevant parameters")
    for param in parameters:
        print(f"- {param.name}")
    
    # Get amp recommendations
    amp_recommendations = kb.get_amp_recommendations(test_text)
    print(f"Amp recommendations: {amp_recommendations}")
    
    print("\n=== PHASE 2 TEST COMPLETED ===")

if __name__ == "__main__":
    test_phase2_features()
