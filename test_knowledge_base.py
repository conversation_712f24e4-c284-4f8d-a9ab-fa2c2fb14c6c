#!/usr/bin/env python3
"""
Test script for Fractal Knowledge Base
"""

from fractal_knowledge import FractalKnowledgeBase

def test_knowledge_base():
    """Test the knowledge base functionality"""
    print("Testing Fractal Audio Knowledge Base...")
    
    # Initialize knowledge base
    kb = FractalKnowledgeBase()
    
    # Test product information
    print("\n=== PRODUCT INFORMATION ===")
    axe_fx_iii = kb.get_product_info("Axe-Fx III")
    if axe_fx_iii:
        print(f"Product: {axe_fx_iii.name}")
        print(f"Category: {axe_fx_iii.category}")
        print(f"Amp blocks: {axe_fx_iii.amp_blocks}")
        print(f"Description: {axe_fx_iii.description}")
    
    # Test relevant content finding
    print("\n=== RELEVANT CONTENT SEARCH ===")
    test_queries = [
        "I'm having trouble with fizz in my high gain tone",
        "How do I set up FRFR speakers?",
        "My guitar volume knob doesn't clean up the amp",
        "What amp model is good for metal?"
    ]
    
    for query in test_queries:
        print(f"\nQuery: {query}")
        relevant_docs = kb.find_relevant_content(query, max_topics=2)
        if relevant_docs:
            for doc in relevant_docs:
                print(f"Found: {doc[:100]}...")
        else:
            print("No relevant documentation found")
    
    # Test troubleshooting
    print("\n=== TROUBLESHOOTING HELP ===")
    troubleshooting_queries = [
        "fizz and noise in high gain",
        "cutting through the mix",
        "string definition problems"
    ]
    
    for query in troubleshooting_queries:
        print(f"\nTroubleshooting: {query}")
        help_text = kb.get_troubleshooting_help(query)
        if help_text:
            print(f"Found help: {help_text[:150]}...")
        else:
            print("No troubleshooting help found")
    
    # Test amp recommendations
    print("\n=== AMP RECOMMENDATIONS ===")
    style_queries = [
        "metal music",
        "clean jazz tones",
        "classic rock",
        "blues guitar"
    ]
    
    for style in style_queries:
        print(f"\nStyle: {style}")
        recommendations = kb.get_amp_recommendations(style)
        if recommendations:
            print(f"Recommended amps: {', '.join(recommendations[:5])}")
        else:
            print("No recommendations found")
    
    # Test system instruction enhancement
    print("\n=== SYSTEM INSTRUCTION ===")
    enhanced_instruction = kb.get_system_instruction_enhancement()
    print(f"Enhanced instruction length: {len(enhanced_instruction)} characters")
    print(f"First 200 characters: {enhanced_instruction[:200]}...")
    
    print("\n=== TEST COMPLETED ===")

if __name__ == "__main__":
    test_knowledge_base()
