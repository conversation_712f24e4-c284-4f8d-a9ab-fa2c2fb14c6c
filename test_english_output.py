#!/usr/bin/env python3
"""
Test script to verify English language output
"""

from fractal_knowledge import FractalKnowledgeBase
from fractal_parameters import FractalParameterDatabase

def test_english_output():
    """Test that all outputs are in English"""
    print("Testing English Language Output...")
    
    # Test 1: Knowledge Base System Instruction
    print("\n=== SYSTEM INSTRUCTION TEST ===")
    kb = FractalKnowledgeBase(enable_wiki_fetching=False)
    system_instruction = kb.get_system_instruction_enhancement()
    
    # Check for English phrases
    english_phrases = [
        "You are an expert",
        "Always respond in English",
        "FRACTAL AUDIO PRODUCTS",
        "technical accuracy",
        "best practices"
    ]
    
    for phrase in english_phrases:
        if phrase in system_instruction:
            print(f"✓ Found English phrase: '{phrase}'")
        else:
            print(f"✗ Missing English phrase: '{phrase}'")
    
    # Test 2: Parameter Database
    print("\n=== PARAMETER DATABASE TEST ===")
    param_db = FractalParameterDatabase()
    
    # Test parameter explanation formatting
    bias_param = param_db.get_parameter("Master Bias Excursion")
    if bias_param:
        explanation = param_db.format_parameter_explanation(bias_param)
        
        # Check for English formatting
        english_terms = ["Range:", "Default:", "Usage Tips:", "Related Parameters:"]
        for term in english_terms:
            if term in explanation:
                print(f"✓ Found English term: '{term}'")
            else:
                print(f"✗ Missing English term: '{term}'")
    
    # Test 3: Content Finding
    print("\n=== CONTENT FINDING TEST ===")
    test_query = "I need help with bias excursion settings for high gain tones"
    relevant_content = kb.find_relevant_content(test_query, max_topics=1, include_parameters=True)
    
    if relevant_content:
        print(f"✓ Found {len(relevant_content)} relevant content pieces")
        # Check first piece for English content
        first_content = relevant_content[0]
        if "bias excursion" in first_content.lower():
            print("✓ Content contains relevant technical terms")
        else:
            print("✗ Content missing expected technical terms")
    else:
        print("✗ No relevant content found")
    
    # Test 4: Troubleshooting Help
    print("\n=== TROUBLESHOOTING TEST ===")
    troubleshooting_help = kb.get_troubleshooting_help("fizz in high gain")
    if troubleshooting_help:
        print("✓ Troubleshooting help found")
        if "Problem:" in troubleshooting_help and "Solutions:" in troubleshooting_help:
            print("✓ Troubleshooting format is in English")
        else:
            print("✗ Troubleshooting format not in English")
    else:
        print("✗ No troubleshooting help found")
    
    # Test 5: Amp Recommendations
    print("\n=== AMP RECOMMENDATIONS TEST ===")
    amp_recs = kb.get_amp_recommendations("metal music")
    if amp_recs:
        print(f"✓ Found {len(amp_recs)} amp recommendations: {', '.join(amp_recs[:3])}")
    else:
        print("✗ No amp recommendations found")
    
    print("\n=== ENGLISH OUTPUT TEST COMPLETED ===")
    print("All major components should now output in English!")

if __name__ == "__main__":
    test_english_output()
